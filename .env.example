# ---------------------------------------------------------------------------- #
#                              BACK END VARIABLES                              #
# ---------------------------------------------------------------------------- #
DATABASE_URL=postgres://postgres:postgres@localhost:5432/postgres

# RabbitMQ Configuration
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest
RABBITMQ_VHOST=/

BETTER_AUTH_SECRET="sbzu1VXGu27gNWUl1AONVNQLHuzDJRig"
BETTER_AUTH_URL="http://localhost:8888"

BASE_URL="localhost:8888"
ORIGIN_URL="http://localhost:3333"

# Sentry/Glitchtip Configuration
MONITORING_DSN="http://<EMAIL>"

# ---------------------------------------------------------------------------- #
#                              FRONT END VARIABLES                             #
# ---------------------------------------------------------------------------- #
VITE_API_URL="http://localhost:8888"
