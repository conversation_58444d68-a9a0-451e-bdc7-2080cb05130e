#!/bin/bash

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

echo "🚀 Starting project setup..."

# Install Bun if not already installed
if ! command_exists bun; then
    echo "📦 Installing Bun..."
    curl -fsSL https://bun.sh/install | bash
    # Reload shell configuration
    source ~/.zshrc
else
    echo "✅ Bun is already installed"
fi

# Install Lefthook if not already installed
if ! command_exists lefthook; then
    echo "📦 Installing Lefthook..."
    brew install lefthook
else
    echo "✅ Lefthook is already installed"
fi

# Install actionlint
if ! command_exists actionlint; then
    echo "📦 Installing actionlint..."
    if [[ "$(uname)" == "Darwin" ]]; then
        # macOS installation
        brew install actionlint
    else
        # Linux installation
        bash <(curl https://raw.githubusercontent.com/rhysd/actionlint/main/scripts/download-actionlint.bash)
        sudo mv ./actionlint /usr/local/bin/
    fi
else
    echo "✅ actionlint is already installed"
fi

# Copy .env.example to .env
cp .env.example .env

# Initialize Lefthook
echo "🔧 Initializing Lefthook..."
lefthook install

echo "📦 Installing dependencies..."
bun install

echo "✨ Setup complete! Your development environment is ready."
