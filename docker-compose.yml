services:
  postgres:
    image: postgres:17.4
    restart: always
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=postgres
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  # RabbitMQ with management plugin
  rabbitmq:
    image: rabbitmq:4-management
    restart: always
    ports:
      - "5672:5672" # AMQP protocol port
      - "15672:15672" # Management UI port
    environment:
      - RABBITMQ_DEFAULT_USER=guest
      - RABBITMQ_DEFAULT_PASS=guest
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "-q", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  worker:
    build:
      context: .
      dockerfile: apps/server/Dockerfile.dev
    restart: always
    command: ["bun", "run", "--hot", "src/worker.ts"]
    volumes:
      - ./apps/server/src:/app/apps/server/src
      - ./packages:/app/packages
    depends_on:
      rabbitmq:
        condition: service_healthy
      postgres:
        condition: service_healthy
    environment:
      - NODE_ENV=development
      - DATABASE_URL=******************************************/postgres
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_USER=guest
      - RABBITMQ_PASSWORD=guest
      - RABBITMQ_VHOST=/
      - ENABLE_BACKGROUND_JOBS=true
      - BASE_URL=localhost:8888
      - ORIGIN_URL=http://localhost:3333
    healthcheck:
      test:
        [
          "CMD",
          "ps",
          "aux",
          "|",
          "grep",
          "worker.ts",
          "|",
          "grep",
          "-v",
          "grep",
        ]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 10s

  # ==========================
  # Optional pgAdmin Container
  # ==========================
  # pgadmin:
  #   image: dpage/pgadmin4:latest
  #   environment:
  #     PGADMIN_DEFAULT_EMAIL: <EMAIL>
  #     PGADMIN_DEFAULT_PASSWORD: admin
  #   ports:
  #     - "5050:80"
  #   volumes:
  #     - pgadmin_data:/var/lib/pgadmin
  #   depends_on:
  #     postgres:
  #       condition: service_healthy

volumes:
  postgres_data:
    name: postgres_data
  rabbitmq_data:
    name: rabbitmq_data
