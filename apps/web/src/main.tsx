import React from "react"
import ReactDOM from "react-dom/client"
import App from "./App.tsx"
import "@k22i/ui/globals.css"
import Loader from "../../../packages/ui/src/components/loader.tsx"
import { isProduction } from "./common/utils.ts"

const rootElement = document.querySelector("#root") as Element
if (!rootElement.innerHTML) {
    if (isProduction) {
        const script = document.createElement("script")
        script.src = "https://analytics.k22i.net/script.js"
        script.defer = true
        script.setAttribute(
            "data-website-id",
            "6578dd03-ad97-4f16-a1e3-cbf4c11afb42"
        )
        document.body.appendChild(script)
    } else {
        console.log("Analytics is disabled in development mode")
    }

    const root = ReactDOM.createRoot(rootElement)
    root.render(
        <React.StrictMode>
            <React.Suspense fallback={<Loader full size={50} />}>
                <App />
            </React.Suspense>
        </React.StrictMode>
    )
}
