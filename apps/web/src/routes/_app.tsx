import { AppSidebar } from "@/components/ui/app-sidebar/sidebar"
import { useAuth } from "@/hooks/useAuth"
import { SidebarInset, SidebarProvider, SidebarTrigger } from "@k22i/ui/sidebar"
import { Outlet, createFileRoute } from "@tanstack/react-router"

export const Route = createFileRoute("/_app")({
    component: AppLayout,
})

function AppLayout() {
    useAuth()
    return (
        <div className="flex w-full justify-center bg-sidebar">
            <div className="flex w-full max-w-[2160px]">
                <SidebarProvider>
                    <AppSidebar className="left-auto" variant="inset" />
                    <SidebarInset>
                        <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
                            <div className="flex items-center gap-2 px-4">
                                <SidebarTrigger className="-ml-1" />
                            </div>
                        </header>
                        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
                            <Outlet />
                        </div>
                    </SidebarInset>
                </SidebarProvider>
            </div>
        </div>
    )
}
