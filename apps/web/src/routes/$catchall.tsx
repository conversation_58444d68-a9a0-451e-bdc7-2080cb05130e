import { But<PERSON> } from "@k22i/ui/button"
import {
    <PERSON>,
    CardContent,
    CardDescription,
    CardFooter,
    CardHeader,
    CardTitle,
} from "@k22i/ui/card"
import { Link, createFileRoute } from "@tanstack/react-router"
import { Compass, Home, LogIn } from "lucide-react"
import { useEffect, useState } from "react"

export const Route = createFileRoute("/$catchall")({
    component: NotFoundPage,
})

function NotFoundPage() {
    const [isVisible, setIsVisible] = useState(false)

    useEffect(() => {
        const timer = setTimeout(() => setIsVisible(true), 100)
        return () => clearTimeout(timer)
    }, [])

    return (
        <div className="flex min-h-dvh w-full items-center justify-center overflow-hidden bg-gray-50 p-4 dark:bg-sidebar">
            <div
                className={`transform transition-all duration-700 ${isVisible ? "translate-y-0 opacity-100" : "translate-y-8 opacity-0"}`}
            >
                <Card className="relative w-full max-w-md overflow-hidden border-2">
                    {/* Decorative elements */}
                    <div className="-top-16 -right-16 absolute h-32 w-32 animate-pulse rounded-full bg-primary/5 blur-xl"></div>
                    <div className="-bottom-16 -left-16 absolute h-32 w-32 animate-pulse rounded-full bg-primary/5 blur-xl delay-700"></div>

                    <CardHeader className="relative z-10 text-center">
                        <div className="group relative mx-auto mb-6 flex h-24 w-24 items-center justify-center overflow-hidden rounded-full bg-muted">
                            <div className="absolute inset-0 bg-primary/5 transition-colors duration-300 group-hover:bg-primary/10"></div>
                            <div className="relative animate-bounce-slow">
                                <Compass className="h-12 w-12 text-primary" />
                            </div>
                            <div className="absolute top-1/2 h-1 w-full animate-slide-right bg-gradient-to-r from-transparent via-primary/20 to-transparent"></div>
                        </div>
                        <CardTitle className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text pb-1 font-bold text-5xl text-transparent">
                            404
                        </CardTitle>
                        <CardDescription className="mt-2 font-medium text-xl">
                            You've Wandered Off the Map
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="px-8 text-center">
                        <p className="font-light text-muted-foreground text-sm leading-relaxed">
                            Looks like you've discovered uncharted territory!
                            The page you're looking for has either been moved or
                            never existed in the first place.
                        </p>
                    </CardContent>
                    <CardFooter className="flex justify-center gap-4">
                        <Button
                            asChild
                            variant="outline"
                            className="group transition-all duration-300 hover:border-primary/50"
                        >
                            <Link to="/" className="flex items-center gap-2">
                                <Home
                                    size={16}
                                    className="transition-transform duration-300 group-hover:rotate-12"
                                />
                                Return Home
                            </Link>
                        </Button>
                        <Button>
                            <Link
                                to="/login"
                                className="flex items-center gap-2 px-4"
                            >
                                Login
                                <LogIn
                                    size={16}
                                    className="transition-transform group-hover:translate-x-1"
                                />
                            </Link>
                        </Button>
                    </CardFooter>
                </Card>
            </div>
        </div>
    )
}
