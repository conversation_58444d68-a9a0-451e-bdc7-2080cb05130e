import { But<PERSON> } from "@k22i/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@k22i/ui/card"
import { createFileRoute } from "@tanstack/react-router"
import { ArrowRightLeft } from "lucide-react"

export const Route = createFileRoute("/_app/transfers/")({
    component: TransfersPage,
})

function TransfersPage() {
    return (
        <div className="container mx-auto py-6">
            <div className="mb-6 flex items-center justify-between">
                <h1 className="font-bold text-3xl">Transfers</h1>
                <Button>
                    <ArrowRightLeft className="mr-2 h-4 w-4" />
                    New Transfer
                </Button>
            </div>

            <Card>
                <CardHeader>
                    <CardTitle>Recent Transfers</CardTitle>
                </CardHeader>
                <CardContent>
                    <p>Transfer history will be displayed here.</p>
                </CardContent>
            </Card>
        </div>
    )
}
