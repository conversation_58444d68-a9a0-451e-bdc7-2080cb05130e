import { createFileRoute } from "@tanstack/react-router"
import { z } from "zod"

import ResetPasswordForm from "@/features/auth/ResetPasswordForm"
import type { FC } from "react"

const ResetPassword: FC = () => {
    return (
        <main className="flex h-dvh w-full items-center justify-center bg-gray-100 dark:bg-sidebar">
            <ResetPasswordForm />
        </main>
    )
}

export const Route = createFileRoute("/_auth/reset-password")({
    validateSearch: z.object({
        token: z.string().optional(),
        error: z.string().optional(),
    }),
    component: ResetPassword,
})
