import { createFileRoute } from "@tanstack/react-router"
import { z } from "zod"

import { LoginForm } from "@/features/auth/LoginForm"
import type { FC } from "react"

const Login: FC = () => {
    return (
        <main className="relative flex h-dvh w-full items-center justify-center overflow-hidden bg-gray-50 dark:bg-sidebar">
            <LoginForm />
        </main>
    )
}

export const Route = createFileRoute("/_auth/login")({
    validateSearch: z.object({
        redirect: z.string().optional().catch(""),
    }),
    component: Login,
})
