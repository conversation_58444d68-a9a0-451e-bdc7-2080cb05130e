import { createFileRoute } from "@tanstack/react-router"
import { z } from "zod"

import { ForgotPasswordForm } from "@/features/auth/ForgotPasswordForm"
import type { FC } from "react"

const ForgotPassword: FC = () => {
    return (
        <main className="flex h-dvh w-full items-center justify-center bg-gray-100 dark:bg-sidebar">
            <ForgotPasswordForm />
        </main>
    )
}

export const Route = createFileRoute("/_auth/forgot-password")({
    validateSearch: z.object({
        redirect: z.string().optional().catch(""),
    }),
    component: ForgotPassword,
})
