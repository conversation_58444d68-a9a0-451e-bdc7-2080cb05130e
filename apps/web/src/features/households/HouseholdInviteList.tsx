import { authClient } from "@/lib/auth"
import { <PERSON><PERSON> } from "@k22i/ui/badge"
import { <PERSON><PERSON> } from "@k22i/ui/button"
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@k22i/ui/card"
import { Skeleton } from "@k22i/ui/skeleton"
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@k22i/ui/table"
import { formatDistanceToNow } from "date-fns"
import { Ban, Loader2, RefreshCw } from "lucide-react"
import { useState } from "react"
import { toast } from "sonner"
import type { HouseholdInvitation } from "./types"

export function HouseholdInviteList() {
    const [cancellingInvitation, setCancellingInvitation] = useState<
        string | null
    >(null)
    const [resendingInvitation, setResendingInvitation] = useState<
        string | null
    >(null)

    const {
        data: activeOrganization,
        error: activeOrganizationError,
        isPending: activeOrganizationIsPending,
    } = authClient.useActiveOrganization()

    // Note: This is a placeholder as the hook might not exist in the current version
    // Replace with the actual implementation when available
    const {
        data: invitations = [],
        error: invitationsError,
        isPending: invitationsIsPending,
        refetch: refetchInvitations = () => {},
    } = { data: [], error: null, isPending: false, refetch: () => {} }

    const handleCancelInvitation = async (invitationId: string) => {
        try {
            setCancellingInvitation(invitationId)
            await authClient.organization.cancelInvitation({
                invitationId,
            })
            toast.success("Invitation cancelled successfully!")
            refetchInvitations()
        } catch (error) {
            console.error("Failed to cancel invitation:", error)
            toast.error("Failed to cancel invitation. Please try again.")
        } finally {
            setCancellingInvitation(null)
        }
    }

    const handleResendInvitation = async (invitation: HouseholdInvitation) => {
        try {
            setResendingInvitation(invitation.id)
            await authClient.organization.inviteMember({
                email: invitation.email,
                role: invitation.role,
                organizationId: invitation.organizationId,
                resend: true,
            })
            toast.success(`Invitation resent to ${invitation.email}!`)
            refetchInvitations()
        } catch (error) {
            console.error("Failed to resend invitation:", error)
            toast.error("Failed to resend invitation. Please try again.")
        } finally {
            setResendingInvitation(null)
        }
    }

    if (activeOrganizationIsPending || invitationsIsPending) {
        return (
            <Card>
                <CardHeader>
                    <Skeleton className="h-7 w-1/3" />
                    <Skeleton className="h-5 w-1/2" />
                </CardHeader>
                <CardContent>
                    <Skeleton className="h-40 w-full" />
                </CardContent>
            </Card>
        )
    }

    if (activeOrganizationError || invitationsError || !activeOrganization) {
        return (
            <Card>
                <CardHeader>
                    <CardTitle>Error</CardTitle>
                    <CardDescription>
                        Failed to load invitations. Please try again.
                    </CardDescription>
                </CardHeader>
            </Card>
        )
    }

    return (
        <Card>
            <CardHeader>
                <CardTitle>Pending Invitations</CardTitle>
                <CardDescription>
                    Manage invitations to your household
                </CardDescription>
            </CardHeader>
            <CardContent>
                {invitations && invitations.length > 0 ? (
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Email</TableHead>
                                <TableHead>Role</TableHead>
                                <TableHead>Status</TableHead>
                                <TableHead>Invited</TableHead>
                                <TableHead>Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {invitations.map((invitation) => (
                                <TableRow key={invitation.id}>
                                    <TableCell>{invitation.email}</TableCell>
                                    <TableCell>
                                        <Badge variant="outline">
                                            {invitation.role}
                                        </Badge>
                                    </TableCell>
                                    <TableCell>
                                        <Badge
                                            variant={
                                                invitation.status === "pending"
                                                    ? "outline"
                                                    : invitation.status ===
                                                        "accepted"
                                                      ? "default"
                                                      : "destructive"
                                            }
                                        >
                                            {invitation.status}
                                        </Badge>
                                    </TableCell>
                                    <TableCell>
                                        {formatDistanceToNow(
                                            new Date(invitation.createdAt),
                                            {
                                                addSuffix: true,
                                            }
                                        )}
                                    </TableCell>
                                    <TableCell>
                                        <div className="flex space-x-2">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() =>
                                                    handleResendInvitation(
                                                        invitation as HouseholdInvitation
                                                    )
                                                }
                                                disabled={
                                                    resendingInvitation ===
                                                        invitation.id ||
                                                    cancellingInvitation ===
                                                        invitation.id ||
                                                    invitation.status !==
                                                        "pending"
                                                }
                                            >
                                                {resendingInvitation ===
                                                invitation.id ? (
                                                    <Loader2 className="h-4 w-4 animate-spin" />
                                                ) : (
                                                    <RefreshCw className="h-4 w-4" />
                                                )}
                                            </Button>
                                            <Button
                                                variant="destructive"
                                                size="sm"
                                                onClick={() =>
                                                    handleCancelInvitation(
                                                        invitation.id
                                                    )
                                                }
                                                disabled={
                                                    cancellingInvitation ===
                                                        invitation.id ||
                                                    resendingInvitation ===
                                                        invitation.id ||
                                                    invitation.status !==
                                                        "pending"
                                                }
                                            >
                                                {cancellingInvitation ===
                                                invitation.id ? (
                                                    <Loader2 className="h-4 w-4 animate-spin" />
                                                ) : (
                                                    <Ban className="h-4 w-4" />
                                                )}
                                            </Button>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                ) : (
                    <div className="flex h-24 items-center justify-center rounded-md border border-dashed">
                        <p className="text-muted-foreground text-sm">
                            No pending invitations
                        </p>
                    </div>
                )}
            </CardContent>
        </Card>
    )
}
