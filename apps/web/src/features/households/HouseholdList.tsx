import { $api } from "@/lib/api"
import { authClient } from "@/lib/auth"
import { Button } from "@k22i/ui/button"
import {
    Card,
    CardContent,
    CardDescription,
    CardFooter,
    CardHeader,
    CardTitle,
} from "@k22i/ui/card"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@k22i/ui/dialog"
import { Skeleton } from "@k22i/ui/skeleton"
import { Check, Home, Plus } from "lucide-react"
import { useState } from "react"
import { HouseholdCreateForm } from "./HouseholdCreateForm"

export function HouseholdList() {
    const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
    const {
        data: memberships,
        error: organizationsError,
        isPending: organizationsIsPending,
    } = $api.useQuery("get", "/users/memberships")

    const {
        data: activeMember,
        error: activeMemberError,
        isPending: activeMemberIsPending,
    } = authClient.useActiveMember()

    const switchOrganization = async (organizationId: string) => {
        try {
            await authClient.organization.setActive({
                organizationId,
            })
            window.location.reload()
        } catch (error) {
            console.error("Failed to switch organization:", error)
        }
    }

    if (organizationsIsPending || activeMemberIsPending) {
        return (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {[1, 2, 3].map((i) => (
                    <Card key={i} className="overflow-hidden">
                        <CardHeader className="pb-0">
                            <Skeleton className="h-5 w-1/2" />
                            <Skeleton className="h-4 w-3/4" />
                        </CardHeader>
                        <CardContent className="pb-0">
                            <Skeleton className="h-20 w-full" />
                        </CardContent>
                        <CardFooter>
                            <Skeleton className="h-9 w-full" />
                        </CardFooter>
                    </Card>
                ))}
            </div>
        )
    }

    if (organizationsError || activeMemberError) {
        return (
            <Card>
                <CardHeader>
                    <CardTitle>Error</CardTitle>
                    <CardDescription>
                        Failed to load households. Please try again.
                    </CardDescription>
                </CardHeader>
            </Card>
        )
    }

    const activeOrganization = memberships.find(
        (membership) =>
            membership.organization.id === activeMember?.organizationId
    )

    if (!activeOrganization) {
        return (
            <Card>
                <CardHeader>
                    <CardTitle>Error</CardTitle>
                    <CardDescription>
                        Failed to load households. Please try again.
                    </CardDescription>
                </CardHeader>
            </Card>
        )
    }

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <h2 className="font-bold text-2xl tracking-tight">
                    Your Households
                </h2>
                <Dialog
                    open={isCreateDialogOpen}
                    onOpenChange={setIsCreateDialogOpen}
                >
                    <DialogTrigger asChild>
                        <Button>
                            <Plus className="mr-2 h-4 w-4" />
                            Create Household
                        </Button>
                    </DialogTrigger>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>Create a new household</DialogTitle>
                            <DialogDescription>
                                Create a new household to manage your finances
                                with family or friends.
                            </DialogDescription>
                        </DialogHeader>
                        <HouseholdCreateForm
                            onSuccess={() => setIsCreateDialogOpen(false)}
                        />
                    </DialogContent>
                </Dialog>
            </div>

            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {memberships.map((membership) => {
                    const isActive = activeOrganization?.id === membership.id
                    return (
                        <Card key={membership.id} className="overflow-hidden">
                            <CardHeader className="pb-0">
                                <CardTitle className="flex items-center gap-2">
                                    <Home className="h-5 w-5" />
                                    {membership.organization.name}
                                </CardTitle>
                                <CardDescription>
                                    {isActive
                                        ? "Current household"
                                        : "Click to switch"}{" "}
                                    ·{" "}
                                    {membership.role.charAt(0).toUpperCase() +
                                        membership.role.slice(1).toLowerCase()}
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="pb-0">
                                <div className="h-20">
                                    <div className="text-muted-foreground text-sm">
                                        <span className="font-medium">
                                            Role:
                                        </span>{" "}
                                        {membership.role
                                            .charAt(0)
                                            .toUpperCase() +
                                            membership.role
                                                .slice(1)
                                                .toLowerCase()}
                                    </div>
                                    {/* Additional household info could go here */}
                                </div>
                            </CardContent>
                            <CardFooter>
                                <Button
                                    variant={isActive ? "outline" : undefined}
                                    className="w-full"
                                    onClick={() =>
                                        switchOrganization(
                                            membership.organization.id
                                        )
                                    }
                                    disabled={isActive}
                                >
                                    {isActive ? (
                                        <>
                                            <Check className="mr-2 h-4 w-4" />
                                            Current
                                        </>
                                    ) : (
                                        "Switch to this household"
                                    )}
                                </Button>
                            </CardFooter>
                        </Card>
                    )
                })}
            </div>
        </div>
    )
}
