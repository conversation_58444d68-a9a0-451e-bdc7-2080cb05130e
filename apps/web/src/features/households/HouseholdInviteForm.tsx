import { authClient } from "@/lib/auth"
import { zodResolver } from "@hookform/resolvers/zod"
import { <PERSON><PERSON> } from "@k22i/ui/button"
import {
    Card,
    CardContent,
    CardDescription,
    CardFooter,
    CardHeader,
    CardTitle,
} from "@k22i/ui/card"
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@k22i/ui/form"
import { Input } from "@k22i/ui/input"
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@k22i/ui/select"
import { Skeleton } from "@k22i/ui/skeleton"
import { Loader2, UserPlus } from "lucide-react"
import { useState } from "react"
import { useForm } from "react-hook-form"
import { toast } from "sonner"
import { z } from "zod"

const formSchema = z.object({
    email: z.string().email({
        message: "Please enter a valid email address.",
    }),
    role: z.enum(["admin", "member"]),
})

type FormValues = z.infer<typeof formSchema>

export function HouseholdInviteForm() {
    const [isSubmitting, setIsSubmitting] = useState(false)

    const {
        data: activeOrganization,
        error: activeOrganizationError,
        isPending: activeOrganizationIsPending,
    } = authClient.useActiveOrganization()

    const form = useForm<FormValues>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            email: "",
            role: "member",
        },
    })

    const onSubmit = async (data: FormValues) => {
        if (!activeOrganization) {
            toast.error("No active household selected.")
            return
        }

        try {
            setIsSubmitting(true)
            await authClient.organization.inviteMember({
                email: data.email,
                role: data.role,
                organizationId: activeOrganization.id,
            })
            toast.success(`Invitation sent to ${data.email}!`)
            form.reset()
        } catch (error) {
            console.error("Failed to send invitation:", error)
            toast.error("Failed to send invitation. Please try again.")
        } finally {
            setIsSubmitting(false)
        }
    }

    if (activeOrganizationIsPending) {
        return (
            <Card>
                <CardHeader>
                    <Skeleton className="h-7 w-1/3" />
                    <Skeleton className="h-5 w-1/2" />
                </CardHeader>
                <CardContent>
                    <div className="space-y-4">
                        <Skeleton className="h-10 w-full" />
                        <Skeleton className="h-10 w-full" />
                    </div>
                </CardContent>
                <CardFooter>
                    <Skeleton className="h-9 w-1/3" />
                </CardFooter>
            </Card>
        )
    }

    if (activeOrganizationError || !activeOrganization) {
        return (
            <Card>
                <CardHeader>
                    <CardTitle>Error</CardTitle>
                    <CardDescription>
                        Failed to load household. Please try again.
                    </CardDescription>
                </CardHeader>
            </Card>
        )
    }

    return (
        <Card>
            <CardHeader>
                <CardTitle>Invite Members</CardTitle>
                <CardDescription>
                    Invite people to join your household
                </CardDescription>
            </CardHeader>
            <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)}>
                    <CardContent className="space-y-4">
                        <FormField
                            control={form.control}
                            name="email"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Email</FormLabel>
                                    <FormControl>
                                        <Input
                                            placeholder="<EMAIL>"
                                            type="email"
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormDescription>
                                        Enter the email address of the person
                                        you want to invite.
                                    </FormDescription>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="role"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Role</FormLabel>
                                    <Select
                                        onValueChange={field.onChange}
                                        defaultValue={field.value}
                                    >
                                        <FormControl>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select a role" />
                                            </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                            <SelectItem value="admin">
                                                Admin
                                            </SelectItem>
                                            <SelectItem value="member">
                                                Member
                                            </SelectItem>
                                        </SelectContent>
                                    </Select>
                                    <FormDescription>
                                        Admins can manage household settings and
                                        members. Members can view and contribute
                                        to the household.
                                    </FormDescription>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                    </CardContent>
                    <CardFooter>
                        <Button type="submit" disabled={isSubmitting}>
                            {isSubmitting ? (
                                <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Sending Invitation...
                                </>
                            ) : (
                                <>
                                    <UserPlus className="mr-2 h-4 w-4" />
                                    Send Invitation
                                </>
                            )}
                        </Button>
                    </CardFooter>
                </form>
            </Form>
        </Card>
    )
}
