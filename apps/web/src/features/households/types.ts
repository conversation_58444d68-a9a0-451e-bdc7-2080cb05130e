import type { components, paths } from "@k22i/lib/schemas"
import type { Invitation, Member, Organization } from "better-auth/types"

export type HouseholdMember =
    paths["/organizations/{organizationId}/members"]["get"]["responses"]["200"]["content"]["application/json"]["data"][number]

export interface HouseholdInvitation extends Invitation {
    inviter: {
        id: string
        name: string
        email: string
        image?: string
    }
}

export interface Household extends Organization {
    members?: HouseholdMember[]
    invitations?: HouseholdInvitation[]
}
