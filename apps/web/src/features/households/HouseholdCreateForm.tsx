import { authClient } from "@/lib/auth"
import { zodResolver } from "@hookform/resolvers/zod"
import { But<PERSON> } from "@k22i/ui/button"
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@k22i/ui/form"
import { Input } from "@k22i/ui/input"
import { Loader2 } from "lucide-react"
import { useState } from "react"
import { useForm } from "react-hook-form"
import { toast } from "sonner"
import { z } from "zod"

const formSchema = z.object({
    name: z
        .string()
        .min(2, {
            message: "Household name must be at least 2 characters.",
        })
        .max(50, {
            message: "Household name must not exceed 50 characters.",
        }),
    slug: z
        .string()
        .min(2, {
            message: "Slug must be at least 2 characters.",
        })
        .max(50, {
            message: "Slug must not exceed 50 characters.",
        })
        .regex(/^[a-z0-9-]+$/, {
            message:
                "Slug can only contain lowercase letters, numbers, and hyphens.",
        }),
})

type FormValues = z.infer<typeof formSchema>

interface HouseholdCreateFormProps {
    onSuccess?: () => void
}

export function HouseholdCreateForm({ onSuccess }: HouseholdCreateFormProps) {
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [slugAvailable, setSlugAvailable] = useState<boolean | null>(null)
    const [isCheckingSlug, setIsCheckingSlug] = useState(false)

    const form = useForm<FormValues>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            name: "",
            slug: "",
        },
    })

    const checkSlugAvailability = async (slug: string) => {
        if (!slug || slug.length < 2) return

        try {
            setIsCheckingSlug(true)
            const { data, error } = await authClient.organization.checkSlug({
                slug,
            })
            if (error) throw error
            setSlugAvailable(data.status)
        } catch (error) {
            console.error("Failed to check slug availability:", error)
            setSlugAvailable(null)
        } finally {
            setIsCheckingSlug(false)
        }
    }

    const onSubmit = async (data: FormValues) => {
        try {
            setIsSubmitting(true)
            await authClient.organization.create({
                name: data.name,
                slug: data.slug,
            })
            toast.success("Household created successfully!")
            form.reset()
            onSuccess?.()
        } catch (error) {
            console.error("Failed to create household:", error)
            toast.error("Failed to create household. Please try again.")
        } finally {
            setIsSubmitting(false)
        }
    }

    return (
        <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                        <FormItem>
                            <FormLabel>Name</FormLabel>
                            <FormControl>
                                <Input placeholder="My Family" {...field} />
                            </FormControl>
                            <FormDescription>
                                This is the name of your household.
                            </FormDescription>
                            <FormMessage />
                        </FormItem>
                    )}
                />

                <FormField
                    control={form.control}
                    name="slug"
                    render={({ field }) => (
                        <FormItem>
                            <FormLabel>Slug</FormLabel>
                            <FormControl>
                                <div className="relative">
                                    <Input
                                        placeholder="my-family"
                                        {...field}
                                        onChange={(e) => {
                                            field.onChange(e)
                                            checkSlugAvailability(
                                                e.target.value
                                            )
                                        }}
                                    />
                                    {isCheckingSlug && (
                                        <div className="-translate-y-1/2 absolute top-1/2 right-3">
                                            <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                                        </div>
                                    )}
                                    {!isCheckingSlug &&
                                        slugAvailable !== null && (
                                            <div className="-translate-y-1/2 absolute top-1/2 right-3">
                                                <div
                                                    className={`h-2 w-2 rounded-full ${
                                                        slugAvailable
                                                            ? "bg-green-500"
                                                            : "bg-destructive"
                                                    }`}
                                                />
                                            </div>
                                        )}
                                </div>
                            </FormControl>
                            <FormDescription>
                                This will be used in URLs for your household.
                            </FormDescription>
                            <FormMessage />
                            {!isCheckingSlug && slugAvailable === false && (
                                <p className="text-destructive text-sm">
                                    This slug is already taken. Please choose
                                    another one.
                                </p>
                            )}
                        </FormItem>
                    )}
                />

                <Button
                    type="submit"
                    className="w-full"
                    disabled={
                        isSubmitting ||
                        isCheckingSlug ||
                        slugAvailable === false
                    }
                >
                    {isSubmitting ? (
                        <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Creating...
                        </>
                    ) : (
                        "Create Household"
                    )}
                </Button>
            </form>
        </Form>
    )
}
