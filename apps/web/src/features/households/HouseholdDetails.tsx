import { authClient } from "@/lib/auth"
import { zodResolver } from "@hookform/resolvers/zod"
import { <PERSON><PERSON> } from "@k22i/ui/button"
import {
    Card,
    CardContent,
    CardDescription,
    CardFooter,
    CardHeader,
    CardTitle,
} from "@k22i/ui/card"
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@k22i/ui/form"
import { Input } from "@k22i/ui/input"
import { Skeleton } from "@k22i/ui/skeleton"
import { Loader2, Save } from "lucide-react"
import { useState } from "react"
import { useForm } from "react-hook-form"
import { toast } from "sonner"
import { z } from "zod"

const formSchema = z.object({
    name: z
        .string()
        .min(2, {
            message: "Household name must be at least 2 characters.",
        })
        .max(50, {
            message: "Household name must not exceed 50 characters.",
        }),
})

type FormValues = z.infer<typeof formSchema>

export function HouseholdDetails() {
    const [isSubmitting, setIsSubmitting] = useState(false)

    const {
        data: activeOrganization,
        error: activeOrganizationError,
        isPending: activeOrganizationIsPending,
    } = authClient.useActiveOrganization()

    const form = useForm<FormValues>({
        resolver: zodResolver(formSchema),
        values: {
            name: activeOrganization?.name || "",
        },
    })

    const onSubmit = async (data: FormValues) => {
        if (!activeOrganization) return

        try {
            setIsSubmitting(true)
            await authClient.organization.update({
                data: {
                    name: data.name,
                },
                organizationId: activeOrganization.id,
            })
            toast.success("Household updated successfully!")
        } catch (error) {
            console.error("Failed to update household:", error)
            toast.error("Failed to update household. Please try again.")
        } finally {
            setIsSubmitting(false)
        }
    }

    if (activeOrganizationIsPending) {
        return (
            <Card>
                <CardHeader>
                    <Skeleton className="h-7 w-1/3" />
                    <Skeleton className="h-5 w-1/2" />
                </CardHeader>
                <CardContent>
                    <Skeleton className="h-10 w-full" />
                </CardContent>
                <CardFooter>
                    <Skeleton className="h-9 w-1/4" />
                </CardFooter>
            </Card>
        )
    }

    if (activeOrganizationError || !activeOrganization) {
        return (
            <Card>
                <CardHeader>
                    <CardTitle>Error</CardTitle>
                    <CardDescription>
                        Failed to load household details. Please try again.
                    </CardDescription>
                </CardHeader>
            </Card>
        )
    }

    return (
        <Card>
            <CardHeader>
                <CardTitle>Household Details</CardTitle>
                <CardDescription>
                    Update your household information
                </CardDescription>
            </CardHeader>
            <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)}>
                    <CardContent className="space-y-4">
                        <FormField
                            control={form.control}
                            name="name"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Name</FormLabel>
                                    <FormControl>
                                        <Input {...field} />
                                    </FormControl>
                                    <FormDescription>
                                        This is the name of your household.
                                    </FormDescription>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                    </CardContent>
                    <CardFooter>
                        <Button
                            type="submit"
                            disabled={isSubmitting || !form.formState.isDirty}
                        >
                            {isSubmitting ? (
                                <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Saving...
                                </>
                            ) : (
                                <>
                                    <Save className="mr-2 h-4 w-4" />
                                    Save Changes
                                </>
                            )}
                        </Button>
                    </CardFooter>
                </form>
            </Form>
        </Card>
    )
}
