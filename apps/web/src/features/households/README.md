# Household Management Components

This directory contains components for managing households (organizations) in the application.

## Components

### HouseholdList
Displays a list of all households the user is a member of and allows creating new households.

### HouseholdCreateForm
Form for creating a new household with name and slug validation.

### HouseholdDetails
Allows viewing and updating household details like name.

### HouseholdInviteForm
Form for inviting new members to a household with role selection.

### HouseholdInviteList
Displays and manages pending invitations to the household.

### HouseholdMemberList
Displays and manages members of the household, including role changes and removal.

## Implementation Notes

These components use the better-auth organization plugin for managing households. Some hooks like `useListInvitations` and `useGetFullOrganization` are placeholders and should be replaced with the actual implementation when available.

## Usage

The main household management page is implemented in `apps/web/src/routes/_app/households/index.tsx` and uses these components in a tabbed interface.
