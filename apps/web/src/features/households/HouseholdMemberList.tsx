import { $api } from "@/lib/api"
import { authClient } from "@/lib/auth"
import { Avatar, AvatarFallback, AvatarImage } from "@k22i/ui/avatar"
import { Badge } from "@k22i/ui/badge"
import { Button } from "@k22i/ui/button"
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@k22i/ui/card"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@k22i/ui/dialog"
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@k22i/ui/dropdown-menu"
import {
    Pagination,
    PaginationContent,
    PaginationItem,
    PaginationNext,
    PaginationPrevious,
} from "@k22i/ui/pagination"
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@k22i/ui/select"
import { Skeleton } from "@k22i/ui/skeleton"
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@k22i/ui/table"
import { formatDistanceToNow } from "date-fns"
import {
    ArrowDown,
    ArrowUp,
    Loader2,
    LogOut,
    MoreHorizontal,
    Shield,
    UserMinus,
} from "lucide-react"
import { useState } from "react"
import { toast } from "sonner"
import type { HouseholdMember } from "./types"

// Sort options
type SortField = "user.email" | "member.createdAt"
type SortOrder = "ASC" | "DESC"

export function HouseholdMemberList() {
    const [removingMemberId, setRemovingMemberId] = useState<string | null>(
        null
    )
    const [confirmRemoveDialogOpen, setConfirmRemoveDialogOpen] =
        useState(false)
    const [selectedMember, setSelectedMember] =
        useState<HouseholdMember | null>(null)
    const [changingRole, setChangingRole] = useState(false)

    // Pagination and sorting state
    const [currentCursor, setCurrentCursor] = useState<string | null>(null)
    const [prevCursors, setPrevCursors] = useState<Array<string>>([])
    const [sortField, setSortField] = useState<SortField>("member.createdAt")
    const [sortOrder, setSortOrder] = useState<SortOrder>("DESC")
    const [limit] = useState(10)

    const {
        data: organizations,
        error: organizationsError,
        isPending: organizationsIsPending,
    } = authClient.useListOrganizations()

    const {
        data: activeMember,
        error: activeMemberError,
        isPending: activeMemberIsPending,
    } = authClient.useActiveMember()

    // Create the column parameter for sorting
    const getColumnParam = () => {
        return [{ key: sortField, order: sortOrder }]
    }

    // API query with pagination and sorting
    const {
        data: membersData,
        isPending: membersIsPending,
        refetch: refetchMembers,
        error: membersError,
    } = $api.useQuery(
        "get",
        "/organizations/{organizationId}/members",
        {
            params: {
                path: {
                    organizationId: activeMember?.organizationId || "",
                },
                query: {
                    cursor: currentCursor || undefined,
                    limit,
                    column: btoa(JSON.stringify(getColumnParam())),
                },
            },
        },
        {
            enabled: !!activeMember,
            staleTime: Infinity,
        }
    )

    // Handle going to next page
    const handleNextPage = () => {
        if (membersData?.cursor) {
            setPrevCursors([...prevCursors, currentCursor || ""])
            setCurrentCursor(membersData.cursor)
        }
    }

    // Handle going to previous page
    const handlePrevPage = () => {
        if (prevCursors.length > 0) {
            const newPrevCursors = [...prevCursors]
            const prevCursor = newPrevCursors.pop()
            setPrevCursors(newPrevCursors)
            setCurrentCursor(prevCursor || null)
        }
    }

    // Handle sort field change
    const handleSortFieldChange = (field: SortField) => {
        setSortField(field)
        // Reset pagination when sort changes
        setCurrentCursor(null)
        setPrevCursors([])
    }

    // Handle sort order change
    const handleSortOrderChange = (order: SortOrder) => {
        setSortOrder(order)
        // Reset pagination when sort changes
        setCurrentCursor(null)
        setPrevCursors([])
    }

    const handleRemoveMember = async () => {
        if (!selectedMember) return

        try {
            // Use the member's ID for removal
            const memberId = selectedMember.id
            setRemovingMemberId(memberId)
            await authClient.organization.removeMember({
                memberIdOrEmail: memberId,
                organizationId: activeOrganization?.id || "",
            })
            toast.success(`${selectedMember.name} removed from household!`)
            setConfirmRemoveDialogOpen(false)
            setSelectedMember(null)
            refetchMembers()
        } catch (error) {
            console.error("Failed to remove member:", error)
            toast.error("Failed to remove member. Please try again.")
        } finally {
            setRemovingMemberId(null)
        }
    }

    const handleChangeRole = async (
        member: HouseholdMember,
        newRole: "admin" | "member" | "owner"
    ) => {
        try {
            setChangingRole(true)
            await authClient.organization.updateMemberRole({
                memberId: member.id,
                role: newRole,
            })
            toast.success(`${member.name}'s role updated to ${newRole}!`)
            refetchMembers()
        } catch (error) {
            console.error("Failed to update member role:", error)
            toast.error("Failed to update member role. Please try again.")
        } finally {
            setChangingRole(false)
        }
    }

    const handleLeaveHousehold = async () => {
        try {
            setRemovingMemberId(activeMember?.id || null)
            await authClient.organization.leave({
                organizationId: activeOrganization?.id || "",
            })
            toast.success("You have left the household!")
            // Redirect to households page or reload
            window.location.href = "/households"
        } catch (error) {
            console.error("Failed to leave household:", error)
            toast.error("Failed to leave household. Please try again.")
        } finally {
            setRemovingMemberId(null)
        }
    }

    const isCurrentUserOwner = activeMember?.role === "owner"
    const canManageMembers = ["owner", "admin"].includes(
        activeMember?.role || ""
    )

    if (
        organizationsIsPending ||
        activeMemberIsPending ||
        (membersIsPending && !membersData)
    ) {
        return (
            <Card>
                <CardHeader>
                    <Skeleton className="h-8 w-1/2" />
                    <Skeleton className="mt-2 h-4 w-3/4" />
                </CardHeader>
                <CardContent>
                    <div className="space-y-4">
                        {[...Array(limit)].map((_, i) => (
                            <div
                                key={i}
                                className="flex items-center space-x-4"
                            >
                                <Skeleton className="h-10 w-10 rounded-full" />
                                <div className="flex-1 space-y-2">
                                    <Skeleton className="h-8 w-20" />
                                </div>
                            </div>
                        ))}
                    </div>
                    <div className="mt-4 flex justify-end space-x-2">
                        <Skeleton className="h-9 w-24" />
                        <Skeleton className="h-9 w-24" />
                    </div>
                </CardContent>
            </Card>
        )
    }

    if (organizationsError || activeMemberError || !activeMember) {
        return (
            <Card>
                <CardHeader>
                    <CardTitle>Error</CardTitle>
                    <CardDescription>
                        Failed to load household members. Please try again.
                    </CardDescription>
                </CardHeader>
            </Card>
        )
    }

    const activeOrganization = organizations?.find(
        (org) => org.id === activeMember?.organizationId
    )

    if (!activeOrganization) {
        return <div>No active organization</div>
    }

    return (
        <>
            <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                    <div>
                        <CardTitle>Household Members</CardTitle>
                        <CardDescription>
                            Manage members of your household
                        </CardDescription>
                    </div>
                    {activeMember && activeMember.role !== "owner" && (
                        <Button
                            variant="destructive"
                            onClick={() => handleLeaveHousehold()}
                            disabled={removingMemberId === activeMember.id}
                        >
                            {removingMemberId === activeMember.id ? (
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            ) : (
                                <LogOut className="mr-2 h-4 w-4" />
                            )}
                            Leave Household
                        </Button>
                    )}
                </CardHeader>
                <CardContent>
                    <div className="mb-4 flex items-center gap-2">
                        <Select
                            value={sortField}
                            onValueChange={(value) =>
                                handleSortFieldChange(value as SortField)
                            }
                        >
                            <SelectTrigger className="w-[180px]">
                                <SelectValue placeholder="Sort by" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="user.email">
                                    Email
                                </SelectItem>
                                <SelectItem value="member.createdAt">
                                    Joined Date
                                </SelectItem>
                            </SelectContent>
                        </Select>

                        <Select
                            value={sortOrder}
                            onValueChange={(value) =>
                                handleSortOrderChange(value as SortOrder)
                            }
                        >
                            <SelectTrigger className="w-[120px]">
                                <SelectValue placeholder="Order" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="ASC">
                                    <div className="flex items-center">
                                        <ArrowUp className="mr-2 h-4 w-4" />
                                        Ascending
                                    </div>
                                </SelectItem>
                                <SelectItem value="DESC">
                                    <div className="flex items-center">
                                        <ArrowDown className="mr-2 h-4 w-4" />
                                        Descending
                                    </div>
                                </SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    {membersError ? (
                        <div className="flex items-center justify-center rounded-md border border-destructive border-dashed ">
                            <div className="py-8 text-center md:py-12">
                                <p className="font-medium text-destructive">
                                    Failed to load members
                                </p>
                                <p className="mt-1 text-muted-foreground text-sm">
                                    There was an error loading the members list
                                </p>
                                <Button
                                    className="mt-3"
                                    size="sm"
                                    variant="outline"
                                    onClick={() => refetchMembers()}
                                >
                                    Try Again
                                </Button>
                            </div>
                        </div>
                    ) : membersData.data.length > 0 ? (
                        <>
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Member</TableHead>
                                        <TableHead>Role</TableHead>
                                        <TableHead>Joined</TableHead>
                                        {canManageMembers && (
                                            <TableHead>Actions</TableHead>
                                        )}
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {membersData.data.map((member) => {
                                        const isCurrentUser =
                                            member.id === activeMember.userId
                                        const canRemove =
                                            canManageMembers &&
                                            !isCurrentUser &&
                                            (isCurrentUserOwner ||
                                                member.role !== "owner")
                                        const canChangeRole =
                                            canManageMembers &&
                                            !isCurrentUser &&
                                            (isCurrentUserOwner ||
                                                member.role !== "owner")

                                        return (
                                            <TableRow key={member.id}>
                                                <TableCell>
                                                    <div className="flex items-center gap-3">
                                                        <Avatar>
                                                            <AvatarImage
                                                                src={
                                                                    member.image
                                                                }
                                                                alt={
                                                                    member.name
                                                                }
                                                            />
                                                            <AvatarFallback>
                                                                {member.name
                                                                    .split(" ")
                                                                    .map(
                                                                        (n) =>
                                                                            n[0]
                                                                    )
                                                                    .join("")}
                                                            </AvatarFallback>
                                                        </Avatar>
                                                        <div>
                                                            <div className="font-medium">
                                                                {member.name}
                                                                {isCurrentUser && (
                                                                    <span className="ml-2 text-muted-foreground text-xs">
                                                                        (You)
                                                                    </span>
                                                                )}
                                                            </div>
                                                            <div className="text-muted-foreground text-sm">
                                                                {member.email}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <Badge
                                                        variant={
                                                            member.role ===
                                                            "owner"
                                                                ? "default"
                                                                : member.role ===
                                                                    "admin"
                                                                  ? "secondary"
                                                                  : "outline"
                                                        }
                                                    >
                                                        {member.role}
                                                    </Badge>
                                                </TableCell>
                                                <TableCell>
                                                    {formatDistanceToNow(
                                                        new Date(
                                                            member.joinedAt
                                                        ),
                                                        {
                                                            addSuffix: true,
                                                        }
                                                    )}
                                                </TableCell>
                                                {canManageMembers &&
                                                    member.id !==
                                                        activeMember?.userId && (
                                                        <TableCell>
                                                            <DropdownMenu>
                                                                <DropdownMenuTrigger
                                                                    asChild
                                                                >
                                                                    <Button
                                                                        variant="ghost"
                                                                        size="sm"
                                                                    >
                                                                        <MoreHorizontal className="h-4 w-4" />
                                                                    </Button>
                                                                </DropdownMenuTrigger>
                                                                <DropdownMenuContent align="end">
                                                                    <DropdownMenuLabel>
                                                                        Actions
                                                                    </DropdownMenuLabel>
                                                                    {canChangeRole && (
                                                                        <>
                                                                            <DropdownMenuItem
                                                                                disabled={
                                                                                    changingRole ||
                                                                                    member.role ===
                                                                                        "owner" ||
                                                                                    member.role ===
                                                                                        "admin"
                                                                                }
                                                                                onClick={() =>
                                                                                    handleChangeRole(
                                                                                        member,
                                                                                        "admin"
                                                                                    )
                                                                                }
                                                                            >
                                                                                <Shield className="mr-2 h-4 w-4" />
                                                                                Make
                                                                                Admin
                                                                            </DropdownMenuItem>
                                                                            <DropdownMenuItem
                                                                                disabled={
                                                                                    changingRole ||
                                                                                    member.role ===
                                                                                        "owner" ||
                                                                                    member.role ===
                                                                                        "member"
                                                                                }
                                                                                onClick={() =>
                                                                                    handleChangeRole(
                                                                                        member,
                                                                                        "member"
                                                                                    )
                                                                                }
                                                                            >
                                                                                <Shield className="mr-2 h-4 w-4" />
                                                                                Make
                                                                                Member
                                                                            </DropdownMenuItem>
                                                                            <DropdownMenuSeparator />
                                                                        </>
                                                                    )}
                                                                    {canRemove && (
                                                                        <DropdownMenuItem
                                                                            className="text-destructive focus:text-destructive"
                                                                            disabled={
                                                                                removingMemberId ===
                                                                                member.id
                                                                            }
                                                                            onClick={() => {
                                                                                setSelectedMember(
                                                                                    member
                                                                                )
                                                                                setConfirmRemoveDialogOpen(
                                                                                    true
                                                                                )
                                                                            }}
                                                                        >
                                                                            <UserMinus className="mr-2 h-4 w-4" />
                                                                            Remove
                                                                        </DropdownMenuItem>
                                                                    )}
                                                                </DropdownMenuContent>
                                                            </DropdownMenu>
                                                        </TableCell>
                                                    )}
                                            </TableRow>
                                        )
                                    })}
                                </TableBody>
                            </Table>

                            <div className="mt-4">
                                <Pagination>
                                    <PaginationContent>
                                        <PaginationItem>
                                            <PaginationPrevious
                                                onClick={handlePrevPage}
                                                aria-disabled={
                                                    prevCursors.length === 0
                                                }
                                                className={
                                                    prevCursors.length === 0
                                                        ? "pointer-events-none opacity-50"
                                                        : ""
                                                }
                                            />
                                        </PaginationItem>
                                        <PaginationItem>
                                            <PaginationNext
                                                onClick={handleNextPage}
                                                aria-disabled={
                                                    !membersData?.cursor
                                                }
                                                className={
                                                    !membersData?.cursor
                                                        ? "pointer-events-none opacity-50"
                                                        : ""
                                                }
                                            />
                                        </PaginationItem>
                                    </PaginationContent>
                                </Pagination>
                            </div>
                        </>
                    ) : (
                        <div className="flex h-24 items-center justify-center rounded-md border border-dashed">
                            <p className="text-muted-foreground text-sm">
                                No members found
                            </p>
                        </div>
                    )}
                </CardContent>
            </Card>

            <Dialog
                open={confirmRemoveDialogOpen}
                onOpenChange={setConfirmRemoveDialogOpen}
            >
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Remove Member</DialogTitle>
                        <DialogDescription>
                            Are you sure you want to remove{" "}
                            {selectedMember?.name} from this household? This
                            action cannot be undone.
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                        <Button
                            variant="outline"
                            onClick={() => setConfirmRemoveDialogOpen(false)}
                        >
                            Cancel
                        </Button>
                        <Button
                            variant="destructive"
                            onClick={handleRemoveMember}
                            disabled={removingMemberId === selectedMember?.id}
                        >
                            {removingMemberId === selectedMember?.id ? (
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            ) : (
                                "Remove"
                            )}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </>
    )
}
