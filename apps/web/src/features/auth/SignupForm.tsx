import { authClient } from "@/lib/auth"
import { Route } from "@/routes/_auth/signup"
import { zodResolver } from "@hookform/resolvers/zod"
import { But<PERSON> } from "@k22i/ui/button"
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@k22i/ui/card"
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@k22i/ui/form"
import { Input } from "@k22i/ui/input"
import { Progress } from "@k22i/ui/progress"
import { Link } from "@tanstack/react-router"
import { Eye, EyeOff, KeyRound, Loader2, Mail, User } from "lucide-react"
import { type FC, useEffect, useState } from "react"
import { useForm } from "react-hook-form"
import { toast } from "sonner"
import { z } from "zod"

const SignupSchema = z.object({
    name: z.string().min(2, "Name must be at least 2 characters"),
    email: z.string().email("Please enter a valid email address"),
    password: z.string().min(8, "Password must be at least 8 characters"),
})

export const SignUpForm: FC = () => {
    const [isPending, setIsPending] = useState(false)
    const [showPassword, setShowPassword] = useState(false)
    const [passwordStrength, setPasswordStrength] = useState(0)

    const navigate = Route.useNavigate()

    const form = useForm<z.infer<typeof SignupSchema>>({
        resolver: zodResolver(SignupSchema),
        defaultValues: {
            name: "",
            email: "",
            password: "",
        },
        criteriaMode: "all",
        mode: "onChange",
    })

    // Calculate password strength
    useEffect(() => {
        const subscription = form.watch((value, { name }) => {
            if (name === "password") {
                const password = value.password as string
                if (!password) {
                    setPasswordStrength(0)
                    return
                }

                let strength = 0
                // Length check
                if (password.length >= 8) strength += 25
                // Contains uppercase
                if (/[A-Z]/.test(password)) strength += 25
                // Contains number
                if (/[0-9]/.test(password)) strength += 25
                // Contains special char
                if (/[^A-Za-z0-9]/.test(password)) strength += 25

                setPasswordStrength(strength)
            }
        })

        return () => subscription.unsubscribe()
    }, [form])

    const onSubmit = async (payload: z.infer<typeof SignupSchema>) => {
        const { email, password, name } = payload
        try {
            setIsPending(true)
            await authClient.signUp.email(
                {
                    email,
                    name,
                    password,
                },
                {
                    onSuccess: async () => {
                        toast.success(
                            "Sign up successful! Please check your email to verify your account."
                        )
                        await navigate({
                            to: "/login",
                        })
                    },
                    onError: (error) => {
                        console.error(error)
                        toast.error("Sign up failed", {
                            description:
                                error.error.message ||
                                "Please check your information and try again",
                        })
                    },
                }
            )
        } catch (_error) {
            toast.error("An unexpected error occurred", {
                description: "Please try again later",
            })
        } finally {
            setIsPending(false)
        }
    }

    return (
        <Card className="mx-auto max-w-md">
            <CardHeader className="space-y-2">
                <CardTitle className="font-bold text-2xl">
                    Create an account
                </CardTitle>
                <CardDescription>
                    Enter your information to create your account
                </CardDescription>
            </CardHeader>
            <CardContent>
                <Form {...form}>
                    <form
                        className="grid gap-5"
                        onSubmit={form.handleSubmit(onSubmit)}
                    >
                        <FormField
                            control={form.control}
                            name="name"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Name</FormLabel>
                                    <div className="relative">
                                        <User className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 text-muted-foreground" />
                                        <FormControl>
                                            <Input
                                                autoComplete="name"
                                                className="pl-10"
                                                placeholder="John Doe"
                                                {...field}
                                            />
                                        </FormControl>
                                    </div>
                                    <FormMessage className="text-destructive" />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="email"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Email</FormLabel>
                                    <div className="relative">
                                        <Mail className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 text-muted-foreground" />
                                        <FormControl>
                                            <Input
                                                autoComplete="email"
                                                className="pl-10"
                                                placeholder="<EMAIL>"
                                                type="email"
                                                {...field}
                                            />
                                        </FormControl>
                                    </div>
                                    <FormMessage className="text-destructive" />
                                </FormItem>
                            )}
                        />
                        <div className="grid gap-4">
                            <FormField
                                control={form.control}
                                name="password"
                                render={({ field }) => (
                                    <FormItem>
                                        <div className="space-y-2 leading-none">
                                            <FormLabel>Password</FormLabel>
                                            <div className="relative">
                                                <KeyRound className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 text-muted-foreground" />
                                                <FormControl>
                                                    <Input
                                                        className="pr-10 pl-10"
                                                        type={
                                                            showPassword
                                                                ? "text"
                                                                : "password"
                                                        }
                                                        {...field}
                                                    />
                                                </FormControl>
                                                <button
                                                    className="-translate-y-1/2 absolute top-1/2 right-3 h-4 w-4 text-muted-foreground"
                                                    onClick={(e) => {
                                                        e.preventDefault()
                                                        setShowPassword(
                                                            !showPassword
                                                        )
                                                    }}
                                                    type="button"
                                                >
                                                    {showPassword ? (
                                                        <EyeOff className="h-4 w-4" />
                                                    ) : (
                                                        <Eye className="h-4 w-4" />
                                                    )}
                                                </button>
                                            </div>
                                        </div>
                                        <div className="mt-2 space-y-1">
                                            <Progress
                                                className="[&>*]:bg-green-600"
                                                value={passwordStrength}
                                            />
                                            <p className="text-muted-foreground text-xs">
                                                {passwordStrength === 0 &&
                                                    "Enter a password"}
                                                {passwordStrength === 25 &&
                                                    "Weak password"}
                                                {passwordStrength === 50 &&
                                                    "Medium password"}
                                                {passwordStrength === 75 &&
                                                    "Good password"}
                                                {passwordStrength === 100 &&
                                                    "Strong password"}
                                            </p>
                                        </div>
                                        <FormMessage className="text-destructive" />
                                    </FormItem>
                                )}
                            />
                        </div>
                        <Button
                            className="mt-2 w-full"
                            disabled={isPending}
                            type="submit"
                        >
                            {isPending ? (
                                <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Creating account...
                                </>
                            ) : (
                                "Create account"
                            )}
                        </Button>
                    </form>
                </Form>
                <div className="mt-6 text-center text-sm">
                    Already have an account?{" "}
                    <Link
                        className="font-medium text-primary underline transition-colors hover:text-primary/80"
                        href="/login"
                        to="/login"
                    >
                        Sign in
                    </Link>
                </div>
            </CardContent>
        </Card>
    )
}
