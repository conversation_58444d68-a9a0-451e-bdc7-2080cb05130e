import { authClient } from "@/lib/auth"
import { Route } from "@/routes/_auth/forgot-password"
import { zodResolver } from "@hookform/resolvers/zod"
import { Button } from "@k22i/ui/button"
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@k22i/ui/card"
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@k22i/ui/form"
import { Input } from "@k22i/ui/input"
import { Link } from "@tanstack/react-router"
import { type FC, useState } from "react"
import { useForm } from "react-hook-form"
import { toast } from "sonner"
import { z } from "zod"

const ForgotPasswordSchema = z.object({
    email: z.string().email(),
})

export const ForgotPasswordForm: FC = () => {
    const [isPending, setIsPending] = useState(false)
    const [isSubmitted, setIsSubmitted] = useState(false)

    const navigate = Route.useNavigate()

    const form = useForm<z.infer<typeof ForgotPasswordSchema>>({
        resolver: zodResolver(ForgotPasswordSchema),
        defaultValues: {
            email: "",
        },
        criteriaMode: "all",
    })

    const onSubmit = async (payload: z.infer<typeof ForgotPasswordSchema>) => {
        const { email } = payload
        try {
            setIsPending(true)
            await authClient.forgetPassword(
                { email, redirectTo: "/reset-password" },
                {
                    onSuccess: () => {
                        setIsSubmitted(true)
                        toast.success("Reset password email sent", {
                            description:
                                "Please check your email for further instructions",
                        })
                    },
                    onError: (error) => {
                        toast.error(error.error.name, {
                            description: error.error.message,
                        })
                    },
                }
            )
        } finally {
            setIsPending(false)
        }
    }

    if (isSubmitted) {
        return (
            <Card className="mx-auto max-w-md">
                <CardHeader className="space-y-2">
                    <div className="mx-auto mb-2 flex h-16 w-16 items-center justify-center rounded-full bg-primary/10">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-8 w-8 text-primary"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                        >
                            <path d="M22 10.5V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v12c0 1.1.9 2 2 2h16a2 2 0 0 0 2-2v-3.5" />
                            <path d="M14 11h8" />
                            <path d="M18 15V7" />
                            <path d="m2 8 7.501 6.001a4 4 0 0 0 4.998 0L22 8" />
                        </svg>
                    </div>
                    <CardTitle className="text-center font-bold text-xl">
                        Check your email
                    </CardTitle>
                    <CardDescription className="text-center">
                        If an account exists with the email address you entered,
                        you will receive an email with instructions on how to
                        reset your password.
                    </CardDescription>
                </CardHeader>
                <CardContent className="pb-6">
                    <div className="grid gap-6">
                        <div className="rounded-lg bg-amber-100/10 p-4">
                            <p className="text-muted-foreground text-sm">
                                <span className="mb-1 block font-medium text-foreground">
                                    💡 Tip:
                                </span>
                                If you don't see the email in your inbox, please
                                check your spam folder or try again in a few
                                minutes.
                            </p>
                        </div>
                        <Button
                            className="group w-full transition-all duration-300"
                            onClick={() => navigate({ to: "/login" })}
                        >
                            <span className="group-hover:-translate-x-1 mr-2 inline-block transition-transform duration-300">
                                ←
                            </span>
                            Back to Login
                        </Button>
                    </div>
                </CardContent>
            </Card>
        )
    }

    return (
        <Card className="mx-auto max-w-md">
            <CardHeader className="space-y-2">
                <div className="mx-auto mb-2 flex h-16 w-16 items-center justify-center rounded-full bg-primary/10">
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-8 w-8 text-primary"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                    >
                        <rect
                            width="18"
                            height="11"
                            x="3"
                            y="11"
                            rx="2"
                            ry="2"
                        />
                        <path d="M7 11V7a5 5 0 0 1 10 0v4" />
                        <path d="M12 16v.01" />
                    </svg>
                </div>
                <CardTitle className="text-center font-bold text-xl">
                    Forgot Password
                </CardTitle>
                <CardDescription className="text-center">
                    Enter your email address and we'll send you a link to reset
                    your password
                </CardDescription>
            </CardHeader>
            <CardContent className="pb-6">
                <Form {...form}>
                    <form
                        className="grid gap-6"
                        onSubmit={form.handleSubmit(onSubmit)}
                    >
                        <FormField
                            control={form.control}
                            name="email"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Email</FormLabel>
                                    <FormControl>
                                        <Input
                                            autoComplete="email"
                                            className="transition-all duration-300 focus-visible:ring-primary/50"
                                            placeholder="<EMAIL>"
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <Button
                            className="group w-full transition-all duration-300"
                            disabled={isPending}
                            type="submit"
                        >
                            {isPending ? (
                                <>
                                    <svg
                                        className="mr-2 h-4 w-4 animate-spin"
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                    >
                                        <circle
                                            className="opacity-25"
                                            cx="12"
                                            cy="12"
                                            r="10"
                                            stroke="currentColor"
                                            strokeWidth="4"
                                        ></circle>
                                        <path
                                            className="opacity-75"
                                            fill="currentColor"
                                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                        ></path>
                                    </svg>
                                    Sending...
                                </>
                            ) : (
                                <>
                                    Send Reset Link
                                    <svg
                                        className="ml-2 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1"
                                        xmlns="http://www.w3.org/2000/svg"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        strokeWidth="2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                    >
                                        <path d="M5 12h14" />
                                        <path d="m12 5 7 7-7 7" />
                                    </svg>
                                </>
                            )}
                        </Button>
                    </form>
                </Form>
                <div className="mt-6 text-center text-sm">
                    <span className="text-muted-foreground">
                        Remember your password?
                    </span>{" "}
                    <Link
                        className="font-medium text-primary hover:underline"
                        to="/login"
                    >
                        Back to login
                    </Link>
                </div>
            </CardContent>
        </Card>
    )
}
