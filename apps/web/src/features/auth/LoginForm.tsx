import { authClient } from "@/lib/auth"
import { Route } from "@/routes/_auth/login"
import { zodResolver } from "@hookform/resolvers/zod"
import { But<PERSON> } from "@k22i/ui/button"
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@k22i/ui/card"
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@k22i/ui/form"
import { Input } from "@k22i/ui/input"
import { Label } from "@k22i/ui/label"
import { Link } from "@tanstack/react-router"
import { Eye, EyeOff, KeyRound, Loader2, Mail } from "lucide-react"
import { type FC, useState } from "react"
import { useForm } from "react-hook-form"
import { toast } from "sonner"
import { z } from "zod"

const LoginSchema = z.object({
    email: z.string().email("Please enter a valid email address"),
    password: z.string().min(1, "Password is required"),
})

export const LoginForm: FC = () => {
    const [isPending, setIsPending] = useState(false)
    const [showPassword, setShowPassword] = useState(false)

    const navigate = Route.useNavigate()
    const search = Route.useSearch()

    const form = useForm<z.infer<typeof LoginSchema>>({
        resolver: zodResolver(LoginSchema),
        defaultValues: {
            email: "",
            password: "",
        },
        criteriaMode: "all",
    })

    const onSubmit = async (payload: z.infer<typeof LoginSchema>) => {
        const { email, password } = payload
        try {
            setIsPending(true)
            await authClient.signIn.email(
                {
                    email,
                    password,
                },
                {
                    onSuccess: async () => {
                        toast.success("Login successful. Redirecting...")
                        await navigate({
                            to: `/${search.redirect || ""}`,
                        })
                    },
                    onError: (error) => {
                        toast.error("Login failed", {
                            description:
                                error.error.message ||
                                "Please check your credentials and try again",
                        })
                    },
                }
            )
        } catch (_error) {
            toast.error("An unexpected error occurred", {
                description: "Please try again later",
            })
        } finally {
            setIsPending(false)
        }
    }

    return (
        <Card className="mx-auto max-w-md shadow-lg">
            <CardHeader className="space-y-2">
                <CardTitle className="font-bold text-2xl">
                    Welcome back
                </CardTitle>
                <CardDescription>
                    Enter your credentials to access your account
                </CardDescription>
            </CardHeader>
            <CardContent>
                <Form {...form}>
                    <form
                        className="grid gap-5"
                        onSubmit={form.handleSubmit(onSubmit)}
                    >
                        <FormField
                            control={form.control}
                            name="email"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Email</FormLabel>
                                    <div className="relative">
                                        <Mail className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 text-muted-foreground" />
                                        <FormControl>
                                            <Input
                                                autoComplete="email"
                                                className="pl-10"
                                                placeholder="<EMAIL>"
                                                {...field}
                                            />
                                        </FormControl>
                                    </div>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <div className="grid gap-2">
                            <FormField
                                control={form.control}
                                name="password"
                                render={({ field }) => (
                                    <FormItem>
                                        <div className="space-y-2 leading-none">
                                            <div className="flex items-center">
                                                <Label htmlFor="password">
                                                    Password
                                                </Label>
                                                <Link
                                                    className="ml-auto inline-block text-sm underline transition-colors hover:text-primary"
                                                    href="/forgot-password"
                                                    tabIndex={-1}
                                                    to="/forgot-password"
                                                >
                                                    Forgot your password?
                                                </Link>
                                            </div>
                                            <div className="relative">
                                                <KeyRound className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 text-muted-foreground" />
                                                <FormControl>
                                                    <Input
                                                        className="pr-10 pl-10"
                                                        type={
                                                            showPassword
                                                                ? "text"
                                                                : "password"
                                                        }
                                                        {...field}
                                                    />
                                                </FormControl>
                                                <button
                                                    className="-translate-y-1/2 absolute top-1/2 right-3 h-4 w-4 text-muted-foreground"
                                                    onClick={(e) => {
                                                        e.preventDefault()
                                                        setShowPassword(
                                                            !showPassword
                                                        )
                                                    }}
                                                    type="button"
                                                >
                                                    {showPassword ? (
                                                        <EyeOff className="h-4 w-4" />
                                                    ) : (
                                                        <Eye className="h-4 w-4" />
                                                    )}
                                                </button>
                                            </div>
                                        </div>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>
                        <Button
                            className="mt-2 w-full"
                            disabled={isPending}
                            type="submit"
                        >
                            {isPending ? (
                                <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Signing in...
                                </>
                            ) : (
                                "Sign in"
                            )}
                        </Button>
                    </form>
                </Form>
                <div className="mt-6 text-center text-sm">
                    Don&apos;t have an account?{" "}
                    <Link
                        className="font-medium text-primary underline transition-colors hover:text-primary/80"
                        href="/signup"
                        to="/signup"
                    >
                        Create an account
                    </Link>
                </div>
            </CardContent>
        </Card>
    )
}
