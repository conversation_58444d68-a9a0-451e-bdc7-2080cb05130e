import { useAuth } from "@/hooks/useAuth"
import { Card, CardDescription, CardHeader, CardTitle } from "@k22i/ui/card"
import Loader from "@k22i/ui/loader"
import type { FC } from "react"

const DisabledUserCard: FC = () => {
    const { user } = useAuth()
    if (!user) {
        return <Loader />
    }
    return (
        <main className="flex h-dvh w-full items-center justify-center">
            <Card className="max-w-md">
                <CardHeader>
                    <CardTitle>Account is currently disabled.</CardTitle>
                    <CardDescription>
                        The email address <strong>{user.email}</strong> is
                        currently disabled. Please notify the admin to enable
                        your account.
                    </CardDescription>
                </CardHeader>
            </Card>
        </main>
    )
}

export default DisabledUserCard
