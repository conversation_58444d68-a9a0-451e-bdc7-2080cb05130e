import { authClient } from "@/lib/auth"
import { Route } from "@/routes/_auth/reset-password"
import { zodResolver } from "@hookform/resolvers/zod"
import { Button } from "@k22i/ui/button"
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@k22i/ui/card"
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@k22i/ui/form"
import { Input } from "@k22i/ui/input"
import { Link } from "@tanstack/react-router"
import { type FC, useEffect, useState } from "react"
import { useForm } from "react-hook-form"
import { toast } from "sonner"
import { z } from "zod"

const ResetPasswordSchema = z
    .object({
        password: z
            .string()
            .min(8, "Password must be at least 8 characters")
            .max(128, "Password must be less than 128 characters"),
        confirmPassword: z.string(),
    })
    .refine((data) => data.password === data.confirmPassword, {
        message: "Passwords do not match",
        path: ["confirmPassword"],
    })

export const ResetPasswordForm: FC = () => {
    const [isPending, setIsPending] = useState(false)
    const [isSubmitted, setIsSubmitted] = useState(false)
    const [token, setToken] = useState<string | null>(null)
    const [tokenError, setTokenError] = useState<string | null>(null)

    const navigate = Route.useNavigate()
    const search = Route.useSearch()

    useEffect(() => {
        // Get token from URL
        const urlToken = search.token
        if (!urlToken) {
            setTokenError(
                "Reset token is missing. Please request a new password reset link."
            )
            return
        }
        setToken(urlToken)

        // Check for error in URL
        const error = search.error
        if (error) {
            setTokenError(
                error === "invalid_token"
                    ? "Invalid or expired token. Please request a new password reset link."
                    : "An error occurred. Please try again."
            )
        }
    }, [search])

    const form = useForm<z.infer<typeof ResetPasswordSchema>>({
        resolver: zodResolver(ResetPasswordSchema),
        defaultValues: {
            password: "",
            confirmPassword: "",
        },
        criteriaMode: "all",
    })

    const onSubmit = async (payload: z.infer<typeof ResetPasswordSchema>) => {
        if (!token) {
            setTokenError(
                "Reset token is missing. Please request a new password reset link."
            )
            return
        }

        try {
            setIsPending(true)
            await authClient.resetPassword(
                {
                    newPassword: payload.password,
                    token,
                },
                {
                    onSuccess: () => {
                        setIsSubmitted(true)
                        toast.success("Password reset successful", {
                            description:
                                "You can now log in with your new password",
                        })
                    },
                    onError: (error) => {
                        if (error.error.status === 400) {
                            setTokenError(
                                "Invalid or expired token. Please request a new password reset link."
                            )
                        } else {
                            toast.error(error.error.name, {
                                description: error.error.message,
                            })
                        }
                    },
                }
            )
        } finally {
            setIsPending(false)
        }
    }

    if (isSubmitted) {
        return (
            <Card className="mx-auto max-w-md">
                <CardHeader className="space-y-2 pb-6">
                    <div className="mx-auto mb-2 flex h-16 w-16 items-center justify-center rounded-full bg-primary/10">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-8 w-8 text-primary"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                        >
                            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
                            <polyline points="22 4 12 14.01 9 11.01" />
                        </svg>
                    </div>
                    <CardTitle className="text-center font-bold text-xl">
                        Password Reset Successful
                    </CardTitle>
                    <CardDescription className="text-center">
                        Your password has been reset successfully. You can now
                        log in with your new password.
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="grid gap-6">
                        <Button
                            className="group w-full transition-all duration-300"
                            onClick={() => navigate({ to: "/login" })}
                        >
                            <span className="group-hover:-translate-x-1 mr-2 inline-block transition-transform duration-300">
                                ←
                            </span>
                            Back to Login
                        </Button>
                    </div>
                </CardContent>
            </Card>
        )
    }

    if (tokenError) {
        return (
            <Card className="mx-auto max-w-md border-t-destructive">
                <CardHeader className="space-y-2">
                    <div className="mx-auto mb-2 flex h-16 w-16 items-center justify-center rounded-full bg-destructive/10">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-8 w-8 text-destructive"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                        >
                            <circle cx="12" cy="12" r="10" />
                            <line x1="12" y1="8" x2="12" y2="12" />
                            <line x1="12" y1="16" x2="12.01" y2="16" />
                        </svg>
                    </div>
                    <CardTitle className="text-center font-bold text-xl">
                        Password Reset Error
                    </CardTitle>
                    <CardDescription className="text-center">
                        {tokenError}
                    </CardDescription>
                </CardHeader>
                <CardContent className="pb-6">
                    <div className="grid gap-6">
                        <Button
                            className="group w-full transition-all duration-300"
                            onClick={() => navigate({ to: "/forgot-password" })}
                        >
                            Request New Reset Link
                        </Button>
                        <div className="text-center text-sm">
                            <span className="text-muted-foreground">
                                Remember your password?
                            </span>{" "}
                            <Link
                                className="font-medium text-primary hover:underline"
                                to="/login"
                            >
                                Back to login
                            </Link>
                        </div>
                    </div>
                </CardContent>
            </Card>
        )
    }

    return (
        <Card className="mx-auto max-w-md">
            <CardHeader className="space-y-2">
                <div className="mx-auto mb-2 flex h-16 w-16 items-center justify-center rounded-full bg-primary/10">
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-8 w-8 text-primary"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                    >
                        <rect
                            width="18"
                            height="11"
                            x="3"
                            y="11"
                            rx="2"
                            ry="2"
                        />
                        <path d="M7 11V7a5 5 0 0 1 10 0v4" />
                        <path d="M12 16v.01" />
                    </svg>
                </div>
                <CardTitle className="text-center font-bold text-xl">
                    Reset Password
                </CardTitle>
                <CardDescription className="text-center">
                    Enter your new password below to reset your password
                </CardDescription>
            </CardHeader>
            <CardContent className="pb-6">
                <Form {...form}>
                    <form
                        className="grid gap-6"
                        onSubmit={form.handleSubmit(onSubmit)}
                    >
                        <FormField
                            control={form.control}
                            name="password"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>New Password</FormLabel>
                                    <FormControl>
                                        <Input
                                            type="password"
                                            className="transition-all duration-300 focus-visible:ring-primary/50"
                                            placeholder="••••••••"
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="confirmPassword"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Confirm Password</FormLabel>
                                    <FormControl>
                                        <Input
                                            type="password"
                                            className="transition-all duration-300 focus-visible:ring-primary/50"
                                            placeholder="••••••••"
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <Button
                            className="group w-full transition-all duration-300"
                            disabled={isPending}
                            type="submit"
                        >
                            {isPending ? (
                                <>
                                    <svg
                                        className="mr-2 h-4 w-4 animate-spin"
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                    >
                                        <circle
                                            className="opacity-25"
                                            cx="12"
                                            cy="12"
                                            r="10"
                                            stroke="currentColor"
                                            strokeWidth="4"
                                        ></circle>
                                        <path
                                            className="opacity-75"
                                            fill="currentColor"
                                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                        ></path>
                                    </svg>
                                    Resetting...
                                </>
                            ) : (
                                <>
                                    Reset Password
                                    <svg
                                        className="ml-2 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1"
                                        xmlns="http://www.w3.org/2000/svg"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        strokeWidth="2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                    >
                                        <path d="M5 12h14" />
                                        <path d="m12 5 7 7-7 7" />
                                    </svg>
                                </>
                            )}
                        </Button>
                    </form>
                </Form>
                <div className="mt-6 text-center text-sm">
                    <span className="text-muted-foreground">
                        Remember your password?
                    </span>{" "}
                    <Link
                        className="font-medium text-primary hover:underline"
                        to="/login"
                    >
                        Back to login
                    </Link>
                </div>
            </CardContent>
        </Card>
    )
}

export default ResetPasswordForm
