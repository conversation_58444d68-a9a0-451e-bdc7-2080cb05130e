import {
    SidebarGroup,
    SidebarGroupLabel,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
} from "@k22i/ui/sidebar"
import { cn } from "@k22i/ui/utils"
import { Link, useRouterState } from "@tanstack/react-router"
import type { FC } from "react"
import type { NavItemWithValidRoute, SidebarNavItem } from "./types"

export const NavMain: FC<{
    items: Array<SidebarNavItem>
}> = ({ items }) => {
    const router = useRouterState()

    const renderNavItem = (item: NavItemWithValidRoute) => (
        <SidebarMenuItem key={item.title}>
            <SidebarMenuButton
                asChild
                className={cn(
                    router.location.pathname === item.url && "bg-sidebar-accent"
                )}
                tooltip={item.title}
            >
                <Link to={item.url}>
                    {item.icon && <item.icon />}
                    <span>{item.title}</span>
                </Link>
            </SidebarMenuButton>
            {item.children && item.children.length > 0 && (
                <SidebarMenu className="mt-1 ml-4">
                    {item.children.map((child: NavItemWithValidRoute) => (
                        <SidebarMenuItem key={child.title}>
                            <SidebarMenuButton
                                asChild
                                className={cn(
                                    router.location.pathname === child.url &&
                                        "bg-sidebar-accent"
                                )}
                                tooltip={child.title}
                            >
                                <Link to={child.url}>
                                    {child.icon && <child.icon />}
                                    <span>{child.title}</span>
                                </Link>
                            </SidebarMenuButton>
                        </SidebarMenuItem>
                    ))}
                </SidebarMenu>
            )}
        </SidebarMenuItem>
    )

    return (
        <>
            {items.map((item) => {
                // Check if this is a group or a single item
                if ("label" in item && "items" in item) {
                    const group = item
                    return (
                        <SidebarGroup key={group.label}>
                            <SidebarGroupLabel className="uppercase tracking-widest">
                                {group.label}
                            </SidebarGroupLabel>
                            <SidebarMenu>
                                {group.items.map(renderNavItem)}
                            </SidebarMenu>
                        </SidebarGroup>
                    )
                } else {
                    // This is a single item not in a group
                    // Wrap it in a default group e.g. Dashboard
                    return (
                        <SidebarGroup key={item.title}>
                            <SidebarMenu>{renderNavItem(item)}</SidebarMenu>
                        </SidebarGroup>
                    )
                }
            })}
        </>
    )
}
