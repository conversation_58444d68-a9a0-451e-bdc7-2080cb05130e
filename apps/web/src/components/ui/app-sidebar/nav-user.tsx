import { useTheme } from "@/components/providers/theme-provider"
import { authClient } from "@/lib/auth"
import { Avatar, AvatarFallback } from "@k22i/ui/avatar"
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuGroup,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@k22i/ui/dropdown-menu"
import {
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    useSidebar,
} from "@k22i/ui/sidebar"
import { useNavigate } from "@tanstack/react-router"
import type { User } from "better-auth/types"
import {
    BadgeCheck,
    ChevronsUpDown,
    CreditCard,
    LogOut,
    Moon,
    Sparkles,
    Sun,
} from "lucide-react"
import type { FC } from "react"

export const NavUser: FC<{ user: User }> = ({ user }) => {
    const { isMobile } = useSidebar()
    const twoLetterName = user.name
        .split(" ")
        .map((n) => n[0])
        .join("")
    const { setTheme, theme } = useTheme()

    const navigate = useNavigate()

    return (
        <SidebarMenu className="rounded-md shadow">
            <SidebarMenuItem>
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <SidebarMenuButton
                            className="bg-background hover:bg-background/40 data-[state=open]:bg-background/40 data-[state=open]:text-sidebar-accent-foreground"
                            size="lg"
                        >
                            <Avatar className="size-8 rounded-lg">
                                <AvatarFallback className="rounded-lg">
                                    {twoLetterName.toUpperCase()}
                                </AvatarFallback>
                            </Avatar>
                            <div className="grid flex-1 text-left text-sm leading-tight">
                                <span className="truncate font-semibold">
                                    {twoLetterName}
                                </span>
                                <span className="truncate text-xs">
                                    {user.email}
                                </span>
                            </div>
                            <ChevronsUpDown className="ml-auto size-4" />
                        </SidebarMenuButton>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                        align="end"
                        className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
                        side={isMobile ? "bottom" : "right"}
                        sideOffset={4}
                    >
                        <DropdownMenuLabel className="p-0 font-normal">
                            <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                                <Avatar className="size-8 rounded-lg">
                                    <AvatarFallback className="rounded-lg">
                                        {twoLetterName
                                            .toUpperCase()
                                            .slice(0, 2)}
                                    </AvatarFallback>
                                </Avatar>
                                <div className="grid flex-1 text-left text-sm leading-tight">
                                    <span className="truncate font-semibold">
                                        {twoLetterName}
                                    </span>
                                    <span className="truncate text-xs">
                                        {user.email}
                                    </span>
                                </div>
                            </div>
                        </DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuGroup>
                            <DropdownMenuItem>
                                <Sparkles />
                                Upgrade to Pro
                            </DropdownMenuItem>
                        </DropdownMenuGroup>
                        <DropdownMenuSeparator />
                        <DropdownMenuGroup>
                            <DropdownMenuItem>
                                <BadgeCheck />
                                Account
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                                <CreditCard />
                                Billing
                            </DropdownMenuItem>
                            <DropdownMenuItem
                                onClick={() => {
                                    setTheme(
                                        theme === "dark" ? "light" : "dark"
                                    )
                                }}
                            >
                                {theme === "dark" ? <Sun /> : <Moon />}
                                Toggle Theme
                            </DropdownMenuItem>
                        </DropdownMenuGroup>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                            onClick={async () => {
                                await authClient.signOut()
                                await navigate({
                                    to: "/login",
                                })
                            }}
                        >
                            <LogOut />
                            Log out
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            </SidebarMenuItem>
        </SidebarMenu>
    )
}
