import { ChevronsUpDown, Home } from "lucide-react"

import { authClient } from "@/lib/auth"

import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuShortcut,
    DropdownMenuTrigger,
} from "@k22i/ui/dropdown-menu"
import {
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    useSidebar,
} from "@k22i/ui/sidebar"
import { Skeleton } from "@k22i/ui/skeleton"

export function HouseholdSwitcher() {
    const { isMobile } = useSidebar()
    const {
        data: organizations,
        error: organizationsError,
        isPending: organizationsIsPending,
    } = authClient.useListOrganizations()

    const {
        data: activeMember,
        error: activeMemberError,
        isPending: activeMemberIsPending,
    } = authClient.useActiveMember()

    const switchOrganization = async (organizationId: string) => {
        try {
            await authClient.organization.setActive({
                organizationId,
            })
            // Refresh the page to update the active organization
            window.location.reload()
        } catch (error) {
            console.error("Failed to switch organization:", error)
        }
    }

    if (organizationsIsPending || activeMemberIsPending) {
        return <Skeleton className="h-10 w-full" />
    }
    if (
        organizationsError ||
        !organizations ||
        activeMemberError ||
        !activeMember
    ) {
        return <div>Error loading organizations</div>
    }

    const activeOrganization = organizations?.find(
        (org) => org.id === activeMember?.organizationId
    )

    if (!activeOrganization) {
        return <div>No active organization</div>
    }

    return (
        <SidebarMenu>
            <SidebarMenuItem>
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <SidebarMenuButton
                            size="lg"
                            className="border-foreground/10 bg-background shadow hover:bg-background/40 data-[state=open]:bg-background/40 data-[state=open]:text-sidebar-accent-foreground"
                        >
                            <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar">
                                {/* <activeOrganization.logo className="size-4" /> */}
                                <Home className="size-4" />
                            </div>
                            <div className="grid flex-1 text-left text-sm leading-tight">
                                <span className="truncate font-semibold text-sm">
                                    {activeOrganization.name}
                                </span>
                                <span className="truncate text-xs">
                                    Active Household
                                </span>
                            </div>
                            <ChevronsUpDown className="ml-auto" />
                        </SidebarMenuButton>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                        className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                        align="start"
                        side={isMobile ? "bottom" : "right"}
                        sideOffset={4}
                    >
                        <DropdownMenuLabel className="text-muted-foreground text-xs">
                            Households
                        </DropdownMenuLabel>
                        {organizations.map((organization, index) => (
                            <DropdownMenuItem
                                key={organization.id}
                                onClick={() =>
                                    switchOrganization(organization.id)
                                }
                                className="gap-2 p-2"
                            >
                                <div className="flex size-6 items-center justify-center rounded-sm border">
                                    {/* <organization.logo className="size-4 shrink-0" /> */}
                                    <Home className="size-4 shrink-0" />
                                </div>
                                {organization.name}
                                <DropdownMenuShortcut>
                                    ⌘{index + 1}
                                </DropdownMenuShortcut>
                            </DropdownMenuItem>
                        ))}
                    </DropdownMenuContent>
                </DropdownMenu>
            </SidebarMenuItem>
        </SidebarMenu>
    )
}
