import type { FileRouteTypes } from "@/routeTree.gen"
import type { LucideIcon } from "lucide-react"

// Extract valid route paths from TanStack Router
type ValidRoutes = FileRouteTypes["to"]

/**
 * Base navigation item interface with required URL
 */
export interface BaseNavItem {
    title: string
    icon?: LucideIcon
    url: ValidRoutes
    children?: Array<BaseNavItem>
}

/**
 * Navigation item with URL that must be a valid TanStack Router route
 */
export type NavItemWithValidRoute = BaseNavItem

/**
 * Navigation group containing multiple navigation items
 */
export interface NavGroup {
    label: string
    items: Array<NavItemWithValidRoute>
}

/**
 * Union type for sidebar navigation items
 */
export type SidebarNavItem = NavItemWithValidRoute | NavGroup
