import {
    Bar<PERSON>hart,
    Bell,
    CreditCard,
    FolderTree,
    House,
    LayoutDashboard,
    PieChart,
    Receipt,
    Settings,
    Tag,
    Users,
} from "lucide-react"

import { useAuth } from "@/hooks/useAuth"
import Loader from "@k22i/ui/loader"
import {
    Sidebar,
    SidebarContent,
    SidebarFooter,
    SidebarHeader,
    SidebarRail,
} from "@k22i/ui/sidebar"
import type { FC } from "react"
import { AppLogo } from "./app-logo"
import { HouseholdSwitcher } from "./household-switcher"
import { NavMain } from "./nav-main"
import { NavUser } from "./nav-user"

import type { SidebarNavItem } from "./types"

const navItems: Array<SidebarNavItem> = [
    {
        title: "Dashboard",
        icon: LayoutDashboard,
        url: "/dashboard",
    },
    {
        label: "Financial Management",
        items: [
            {
                title: "Transactions",
                icon: Receipt,
                url: "/transactions",
            },
            {
                title: "Accounts",
                icon: CreditCard,
                url: "/accounts",
            },
        ],
    },
    {
        label: "Organization",
        items: [
            {
                title: "Categories",
                icon: FolderTree,
                url: "/categories",
            },
            {
                title: "Tags",
                icon: Tag,
                url: "/tags",
            },
        ],
    },
    {
        label: "Analysis",
        items: [
            {
                title: "Reports",
                icon: BarChart,
                url: "/reports",
            },
            {
                title: "Insights",
                icon: PieChart,
                url: "/insights",
            },
        ],
    },
    {
        label: "Collaboration",
        items: [
            {
                title: "Households",
                icon: Users,
                url: "/households",
            },
            {
                title: "Notifications",
                icon: Bell,
                url: "/notifications",
            },
        ],
    },
    {
        label: "System",
        items: [
            {
                title: "Settings",
                icon: Settings,
                url: "/settings",
            },
        ],
    },
]

export const AppSidebar: FC<React.ComponentProps<typeof Sidebar>> = ({
    ...props
}) => {
    const { user } = useAuth()

    if (!user) {
        return <Loader />
    }

    return (
        <Sidebar collapsible="icon" {...props}>
            <SidebarHeader>
                <AppLogo />
            </SidebarHeader>
            <SidebarHeader className="my-2">
                <HouseholdSwitcher />
            </SidebarHeader>
            <SidebarContent>
                <NavMain items={navItems} />
            </SidebarContent>
            <SidebarFooter>
                <NavUser user={user} />
            </SidebarFooter>
            <SidebarRail />
        </Sidebar>
    )
}
