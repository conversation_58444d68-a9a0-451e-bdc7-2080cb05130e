import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { ReactQueryDevtools } from "@tanstack/react-query-devtools"
import { RouterProvider, createRouter } from "@tanstack/react-router"
import type { FC } from "react"
import { Toaster } from "../../../packages/ui/src/components/sonner"
import { ThemeProvider } from "./components/providers/theme-provider"
import { TanStackRouterDevelopmentTools } from "./components/utils/development-tools/TanStackRouterDevelopmentTools"
import { routeTree } from "./routeTree.gen"

const queryClient = new QueryClient()

const Router = createRouter({
    routeTree,
    defaultPreload: "intent",
    defaultPreloadDelay: 240,
})

declare module "@tanstack/react-router" {
    interface Register {
        router: typeof Router
    }
}
const InnerApp: FC = () => {
    return <RouterProvider router={Router} />
}

const App: FC = () => {
    return (
        <QueryClientProvider client={queryClient}>
            <ThemeProvider defaultTheme="system" storageKey="vite-ui-theme">
                <InnerApp />
            </ThemeProvider>
            <TanStackRouterDevelopmentTools
                initialIsOpen={false}
                position="bottom-right"
                router={Router}
                toggleButtonProps={{
                    style: {
                        right: "70px",
                        bottom: "20px",
                    },
                }}
            />
            <ReactQueryDevtools initialIsOpen={false} />
            <Toaster richColors theme="light" />
        </QueryClientProvider>
    )
}

export default App
