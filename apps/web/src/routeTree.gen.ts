/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as AppImport } from './routes/_app'
import { Route as CatchallImport } from './routes/$catchall'
import { Route as IndexImport } from './routes/index'
import { Route as AuthSignupImport } from './routes/_auth/signup'
import { Route as AuthResetPasswordImport } from './routes/_auth/reset-password'
import { Route as AuthLoginImport } from './routes/_auth/login'
import { Route as AuthForgotPasswordImport } from './routes/_auth/forgot-password'
import { Route as AppTransfersIndexImport } from './routes/_app/transfers/index'
import { Route as AppTransactionsIndexImport } from './routes/_app/transactions/index'
import { Route as AppTagsIndexImport } from './routes/_app/tags/index'
import { Route as AppSettingsIndexImport } from './routes/_app/settings/index'
import { Route as AppReportsIndexImport } from './routes/_app/reports/index'
import { Route as AppNotificationsIndexImport } from './routes/_app/notifications/index'
import { Route as AppInsightsIndexImport } from './routes/_app/insights/index'
import { Route as AppHouseholdsIndexImport } from './routes/_app/households/index'
import { Route as AppDashboardIndexImport } from './routes/_app/dashboard/index'
import { Route as AppCategoriesIndexImport } from './routes/_app/categories/index'
import { Route as AppAccountsIndexImport } from './routes/_app/accounts/index'

// Create/Update Routes

const AppRoute = AppImport.update({
  id: '/_app',
  getParentRoute: () => rootRoute,
} as any)

const CatchallRoute = CatchallImport.update({
  id: '/$catchall',
  path: '/$catchall',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

const AuthSignupRoute = AuthSignupImport.update({
  id: '/_auth/signup',
  path: '/signup',
  getParentRoute: () => rootRoute,
} as any)

const AuthResetPasswordRoute = AuthResetPasswordImport.update({
  id: '/_auth/reset-password',
  path: '/reset-password',
  getParentRoute: () => rootRoute,
} as any)

const AuthLoginRoute = AuthLoginImport.update({
  id: '/_auth/login',
  path: '/login',
  getParentRoute: () => rootRoute,
} as any)

const AuthForgotPasswordRoute = AuthForgotPasswordImport.update({
  id: '/_auth/forgot-password',
  path: '/forgot-password',
  getParentRoute: () => rootRoute,
} as any)

const AppTransfersIndexRoute = AppTransfersIndexImport.update({
  id: '/transfers/',
  path: '/transfers/',
  getParentRoute: () => AppRoute,
} as any)

const AppTransactionsIndexRoute = AppTransactionsIndexImport.update({
  id: '/transactions/',
  path: '/transactions/',
  getParentRoute: () => AppRoute,
} as any)

const AppTagsIndexRoute = AppTagsIndexImport.update({
  id: '/tags/',
  path: '/tags/',
  getParentRoute: () => AppRoute,
} as any)

const AppSettingsIndexRoute = AppSettingsIndexImport.update({
  id: '/settings/',
  path: '/settings/',
  getParentRoute: () => AppRoute,
} as any)

const AppReportsIndexRoute = AppReportsIndexImport.update({
  id: '/reports/',
  path: '/reports/',
  getParentRoute: () => AppRoute,
} as any)

const AppNotificationsIndexRoute = AppNotificationsIndexImport.update({
  id: '/notifications/',
  path: '/notifications/',
  getParentRoute: () => AppRoute,
} as any)

const AppInsightsIndexRoute = AppInsightsIndexImport.update({
  id: '/insights/',
  path: '/insights/',
  getParentRoute: () => AppRoute,
} as any)

const AppHouseholdsIndexRoute = AppHouseholdsIndexImport.update({
  id: '/households/',
  path: '/households/',
  getParentRoute: () => AppRoute,
} as any)

const AppDashboardIndexRoute = AppDashboardIndexImport.update({
  id: '/dashboard/',
  path: '/dashboard/',
  getParentRoute: () => AppRoute,
} as any)

const AppCategoriesIndexRoute = AppCategoriesIndexImport.update({
  id: '/categories/',
  path: '/categories/',
  getParentRoute: () => AppRoute,
} as any)

const AppAccountsIndexRoute = AppAccountsIndexImport.update({
  id: '/accounts/',
  path: '/accounts/',
  getParentRoute: () => AppRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/$catchall': {
      id: '/$catchall'
      path: '/$catchall'
      fullPath: '/$catchall'
      preLoaderRoute: typeof CatchallImport
      parentRoute: typeof rootRoute
    }
    '/_app': {
      id: '/_app'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AppImport
      parentRoute: typeof rootRoute
    }
    '/_auth/forgot-password': {
      id: '/_auth/forgot-password'
      path: '/forgot-password'
      fullPath: '/forgot-password'
      preLoaderRoute: typeof AuthForgotPasswordImport
      parentRoute: typeof rootRoute
    }
    '/_auth/login': {
      id: '/_auth/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof AuthLoginImport
      parentRoute: typeof rootRoute
    }
    '/_auth/reset-password': {
      id: '/_auth/reset-password'
      path: '/reset-password'
      fullPath: '/reset-password'
      preLoaderRoute: typeof AuthResetPasswordImport
      parentRoute: typeof rootRoute
    }
    '/_auth/signup': {
      id: '/_auth/signup'
      path: '/signup'
      fullPath: '/signup'
      preLoaderRoute: typeof AuthSignupImport
      parentRoute: typeof rootRoute
    }
    '/_app/accounts/': {
      id: '/_app/accounts/'
      path: '/accounts'
      fullPath: '/accounts'
      preLoaderRoute: typeof AppAccountsIndexImport
      parentRoute: typeof AppImport
    }
    '/_app/categories/': {
      id: '/_app/categories/'
      path: '/categories'
      fullPath: '/categories'
      preLoaderRoute: typeof AppCategoriesIndexImport
      parentRoute: typeof AppImport
    }
    '/_app/dashboard/': {
      id: '/_app/dashboard/'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof AppDashboardIndexImport
      parentRoute: typeof AppImport
    }
    '/_app/households/': {
      id: '/_app/households/'
      path: '/households'
      fullPath: '/households'
      preLoaderRoute: typeof AppHouseholdsIndexImport
      parentRoute: typeof AppImport
    }
    '/_app/insights/': {
      id: '/_app/insights/'
      path: '/insights'
      fullPath: '/insights'
      preLoaderRoute: typeof AppInsightsIndexImport
      parentRoute: typeof AppImport
    }
    '/_app/notifications/': {
      id: '/_app/notifications/'
      path: '/notifications'
      fullPath: '/notifications'
      preLoaderRoute: typeof AppNotificationsIndexImport
      parentRoute: typeof AppImport
    }
    '/_app/reports/': {
      id: '/_app/reports/'
      path: '/reports'
      fullPath: '/reports'
      preLoaderRoute: typeof AppReportsIndexImport
      parentRoute: typeof AppImport
    }
    '/_app/settings/': {
      id: '/_app/settings/'
      path: '/settings'
      fullPath: '/settings'
      preLoaderRoute: typeof AppSettingsIndexImport
      parentRoute: typeof AppImport
    }
    '/_app/tags/': {
      id: '/_app/tags/'
      path: '/tags'
      fullPath: '/tags'
      preLoaderRoute: typeof AppTagsIndexImport
      parentRoute: typeof AppImport
    }
    '/_app/transactions/': {
      id: '/_app/transactions/'
      path: '/transactions'
      fullPath: '/transactions'
      preLoaderRoute: typeof AppTransactionsIndexImport
      parentRoute: typeof AppImport
    }
    '/_app/transfers/': {
      id: '/_app/transfers/'
      path: '/transfers'
      fullPath: '/transfers'
      preLoaderRoute: typeof AppTransfersIndexImport
      parentRoute: typeof AppImport
    }
  }
}

// Create and export the route tree

interface AppRouteChildren {
  AppAccountsIndexRoute: typeof AppAccountsIndexRoute
  AppCategoriesIndexRoute: typeof AppCategoriesIndexRoute
  AppDashboardIndexRoute: typeof AppDashboardIndexRoute
  AppHouseholdsIndexRoute: typeof AppHouseholdsIndexRoute
  AppInsightsIndexRoute: typeof AppInsightsIndexRoute
  AppNotificationsIndexRoute: typeof AppNotificationsIndexRoute
  AppReportsIndexRoute: typeof AppReportsIndexRoute
  AppSettingsIndexRoute: typeof AppSettingsIndexRoute
  AppTagsIndexRoute: typeof AppTagsIndexRoute
  AppTransactionsIndexRoute: typeof AppTransactionsIndexRoute
  AppTransfersIndexRoute: typeof AppTransfersIndexRoute
}

const AppRouteChildren: AppRouteChildren = {
  AppAccountsIndexRoute: AppAccountsIndexRoute,
  AppCategoriesIndexRoute: AppCategoriesIndexRoute,
  AppDashboardIndexRoute: AppDashboardIndexRoute,
  AppHouseholdsIndexRoute: AppHouseholdsIndexRoute,
  AppInsightsIndexRoute: AppInsightsIndexRoute,
  AppNotificationsIndexRoute: AppNotificationsIndexRoute,
  AppReportsIndexRoute: AppReportsIndexRoute,
  AppSettingsIndexRoute: AppSettingsIndexRoute,
  AppTagsIndexRoute: AppTagsIndexRoute,
  AppTransactionsIndexRoute: AppTransactionsIndexRoute,
  AppTransfersIndexRoute: AppTransfersIndexRoute,
}

const AppRouteWithChildren = AppRoute._addFileChildren(AppRouteChildren)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/$catchall': typeof CatchallRoute
  '': typeof AppRouteWithChildren
  '/forgot-password': typeof AuthForgotPasswordRoute
  '/login': typeof AuthLoginRoute
  '/reset-password': typeof AuthResetPasswordRoute
  '/signup': typeof AuthSignupRoute
  '/accounts': typeof AppAccountsIndexRoute
  '/categories': typeof AppCategoriesIndexRoute
  '/dashboard': typeof AppDashboardIndexRoute
  '/households': typeof AppHouseholdsIndexRoute
  '/insights': typeof AppInsightsIndexRoute
  '/notifications': typeof AppNotificationsIndexRoute
  '/reports': typeof AppReportsIndexRoute
  '/settings': typeof AppSettingsIndexRoute
  '/tags': typeof AppTagsIndexRoute
  '/transactions': typeof AppTransactionsIndexRoute
  '/transfers': typeof AppTransfersIndexRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/$catchall': typeof CatchallRoute
  '': typeof AppRouteWithChildren
  '/forgot-password': typeof AuthForgotPasswordRoute
  '/login': typeof AuthLoginRoute
  '/reset-password': typeof AuthResetPasswordRoute
  '/signup': typeof AuthSignupRoute
  '/accounts': typeof AppAccountsIndexRoute
  '/categories': typeof AppCategoriesIndexRoute
  '/dashboard': typeof AppDashboardIndexRoute
  '/households': typeof AppHouseholdsIndexRoute
  '/insights': typeof AppInsightsIndexRoute
  '/notifications': typeof AppNotificationsIndexRoute
  '/reports': typeof AppReportsIndexRoute
  '/settings': typeof AppSettingsIndexRoute
  '/tags': typeof AppTagsIndexRoute
  '/transactions': typeof AppTransactionsIndexRoute
  '/transfers': typeof AppTransfersIndexRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/$catchall': typeof CatchallRoute
  '/_app': typeof AppRouteWithChildren
  '/_auth/forgot-password': typeof AuthForgotPasswordRoute
  '/_auth/login': typeof AuthLoginRoute
  '/_auth/reset-password': typeof AuthResetPasswordRoute
  '/_auth/signup': typeof AuthSignupRoute
  '/_app/accounts/': typeof AppAccountsIndexRoute
  '/_app/categories/': typeof AppCategoriesIndexRoute
  '/_app/dashboard/': typeof AppDashboardIndexRoute
  '/_app/households/': typeof AppHouseholdsIndexRoute
  '/_app/insights/': typeof AppInsightsIndexRoute
  '/_app/notifications/': typeof AppNotificationsIndexRoute
  '/_app/reports/': typeof AppReportsIndexRoute
  '/_app/settings/': typeof AppSettingsIndexRoute
  '/_app/tags/': typeof AppTagsIndexRoute
  '/_app/transactions/': typeof AppTransactionsIndexRoute
  '/_app/transfers/': typeof AppTransfersIndexRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/$catchall'
    | ''
    | '/forgot-password'
    | '/login'
    | '/reset-password'
    | '/signup'
    | '/accounts'
    | '/categories'
    | '/dashboard'
    | '/households'
    | '/insights'
    | '/notifications'
    | '/reports'
    | '/settings'
    | '/tags'
    | '/transactions'
    | '/transfers'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/$catchall'
    | ''
    | '/forgot-password'
    | '/login'
    | '/reset-password'
    | '/signup'
    | '/accounts'
    | '/categories'
    | '/dashboard'
    | '/households'
    | '/insights'
    | '/notifications'
    | '/reports'
    | '/settings'
    | '/tags'
    | '/transactions'
    | '/transfers'
  id:
    | '__root__'
    | '/'
    | '/$catchall'
    | '/_app'
    | '/_auth/forgot-password'
    | '/_auth/login'
    | '/_auth/reset-password'
    | '/_auth/signup'
    | '/_app/accounts/'
    | '/_app/categories/'
    | '/_app/dashboard/'
    | '/_app/households/'
    | '/_app/insights/'
    | '/_app/notifications/'
    | '/_app/reports/'
    | '/_app/settings/'
    | '/_app/tags/'
    | '/_app/transactions/'
    | '/_app/transfers/'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  CatchallRoute: typeof CatchallRoute
  AppRoute: typeof AppRouteWithChildren
  AuthForgotPasswordRoute: typeof AuthForgotPasswordRoute
  AuthLoginRoute: typeof AuthLoginRoute
  AuthResetPasswordRoute: typeof AuthResetPasswordRoute
  AuthSignupRoute: typeof AuthSignupRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  CatchallRoute: CatchallRoute,
  AppRoute: AppRouteWithChildren,
  AuthForgotPasswordRoute: AuthForgotPasswordRoute,
  AuthLoginRoute: AuthLoginRoute,
  AuthResetPasswordRoute: AuthResetPasswordRoute,
  AuthSignupRoute: AuthSignupRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/$catchall",
        "/_app",
        "/_auth/forgot-password",
        "/_auth/login",
        "/_auth/reset-password",
        "/_auth/signup"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/$catchall": {
      "filePath": "$catchall.tsx"
    },
    "/_app": {
      "filePath": "_app.tsx",
      "children": [
        "/_app/accounts/",
        "/_app/categories/",
        "/_app/dashboard/",
        "/_app/households/",
        "/_app/insights/",
        "/_app/notifications/",
        "/_app/reports/",
        "/_app/settings/",
        "/_app/tags/",
        "/_app/transactions/",
        "/_app/transfers/"
      ]
    },
    "/_auth/forgot-password": {
      "filePath": "_auth/forgot-password.tsx"
    },
    "/_auth/login": {
      "filePath": "_auth/login.tsx"
    },
    "/_auth/reset-password": {
      "filePath": "_auth/reset-password.tsx"
    },
    "/_auth/signup": {
      "filePath": "_auth/signup.tsx"
    },
    "/_app/accounts/": {
      "filePath": "_app/accounts/index.tsx",
      "parent": "/_app"
    },
    "/_app/categories/": {
      "filePath": "_app/categories/index.tsx",
      "parent": "/_app"
    },
    "/_app/dashboard/": {
      "filePath": "_app/dashboard/index.tsx",
      "parent": "/_app"
    },
    "/_app/households/": {
      "filePath": "_app/households/index.tsx",
      "parent": "/_app"
    },
    "/_app/insights/": {
      "filePath": "_app/insights/index.tsx",
      "parent": "/_app"
    },
    "/_app/notifications/": {
      "filePath": "_app/notifications/index.tsx",
      "parent": "/_app"
    },
    "/_app/reports/": {
      "filePath": "_app/reports/index.tsx",
      "parent": "/_app"
    },
    "/_app/settings/": {
      "filePath": "_app/settings/index.tsx",
      "parent": "/_app"
    },
    "/_app/tags/": {
      "filePath": "_app/tags/index.tsx",
      "parent": "/_app"
    },
    "/_app/transactions/": {
      "filePath": "_app/transactions/index.tsx",
      "parent": "/_app"
    },
    "/_app/transfers/": {
      "filePath": "_app/transfers/index.tsx",
      "parent": "/_app"
    }
  }
}
ROUTE_MANIFEST_END */
