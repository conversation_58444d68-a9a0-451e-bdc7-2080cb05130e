import { authClient } from "@/lib/auth"
import { useQuery } from "@tanstack/react-query"
import { useNavigate } from "@tanstack/react-router"
import type { Session as BaseSession, User } from "better-auth/types"
import { useEffect } from "react"

interface Session extends BaseSession {
    activeOrganizationId?: string | null | undefined
}

/**
 * Hook for handling authentication state and automatic redirects.
 *
 * @returns The authenticated user or undefined if the data is still loading.
 *
 * @description
 * This hook provides access to the current authenticated user and handles authentication flow.
 * It automatically fetches the user's session using authClient.getSession() and manages the auth state.
 *
 * If the user is not authenticated (no session found or authentication error occurs),
 * this hook will automatically redirect the user to the login page with the current URL
 * as a redirect parameter, allowing them to return to their intended destination after login.
 *
 * The hook refreshes authentication state when the window regains focus for security.
 */
export const useAuth = () => {
    const navigate = useNavigate()

    const {
        data: auth,
        error,
        isLoading,
        isError,
    } = useQuery<{ user: User; session: Session } | null>({
        queryKey: ["auth"],
        queryFn: async () => {
            try {
                const response = await authClient.getSession()
                const { data, error } = response
                if (error || data === null) {
                    return null
                }

                if (data?.user && data?.session) {
                    const { user, session } = data
                    return { user, session }
                }

                return null
            } catch (error) {
                console.error("Auth error:", error)
                return null
            }
        },
        retry: false,
        refetchOnWindowFocus: true,
    })

    useEffect(() => {
        // If we have an error or no auth data, redirect to login
        if (isError || (!isLoading && !auth && !error)) {
            navigate({
                to: "/login",
                search: {
                    redirect: window.location.pathname,
                },
                replace: true,
                reloadDocument: true,
            })
        }
    }, [auth, error, isError, navigate, isLoading])

    return { user: auth?.user, session: auth?.session }
}
