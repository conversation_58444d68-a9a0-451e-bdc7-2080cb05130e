{
    "compilerOptions": {
        "target": "ES2020",
        "useDefineForClassFields": true,
        "lib": ["ES2020", "DOM", "DOM.Iterable"],
        "module": "ESNext",
        "skipLibCheck": true,
        "esModuleInterop": true,

        /* Bundler mode */
        "moduleResolution": "bundler",
        "allowImportingTsExtensions": true,
        "resolveJsonModule": true,
        "isolatedModules": true,
        "noEmit": true,
        "jsx": "react-jsx",
        "verbatimModuleSyntax": true,

        /* Linting */
        "strict": true,
        "noUnusedLocals": true,
        "noUnusedParameters": true,
        "noFallthroughCasesInSwitch": true,
        "noImplicitOverride": true,
        "noImplicitReturns": true,
        "noPropertyAccessFromIndexSignature": true,
        "noUncheckedIndexedAccess": true,

        /* Paths */
        "baseUrl": ".",
        "paths": {
            "@/*": ["./src/*"],
            "@@/*": ["./*"]
        }
    },
    "include": ["src", "e2e", "../../packages/ui/src/hooks/use-mobile.ts"],
    "references": [{ "path": "./tsconfig.node.json" }]
}
