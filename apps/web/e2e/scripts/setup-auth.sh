#!/bin/bash

# Parse command line arguments
HEADLESS=true
while [[ $# -gt 0 ]]; do
  case $1 in
    --no-headless)
      HEADLESS=false
      shift
      ;;
    *)
      shift
      ;;
  esac
done

# Ensure the server is running
echo "Checking if the development server is running..."
if ! curl -s http://localhost:3333 > /dev/null; then
  echo "Starting development server in the background..."
  bun run dev &
  SERVER_PID=$!

  # Give the server time to start
  echo "Waiting for server to start..."
  sleep 10

  # Check if server started successfully
  if ! curl -s http://localhost:3333 > /dev/null; then
    echo "Failed to start development server. Please start it manually with 'bun run dev' and try again."
    kill $SERVER_PID 2>/dev/null
    exit 1
  fi

  echo "Development server started successfully."
  KILL_SERVER=true
else
  echo "Development server is already running."
  KILL_SERVER=false
fi

# Create the auth directory
mkdir -p e2e/.auth

# Run the setup script
echo "Running authentication setup script..."
if [ "$HEADLESS" = false ]; then
  echo "Running in non-headless mode..."
  HEADLESS=false bun run e2e/setup-auth.ts
else
  echo "Running in headless mode..."
  HEADLESS=true bun run e2e/setup-auth.ts
fi

# Check if the auth file was created
if [ -s "e2e/.auth/user.json" ]; then
  echo "Authentication setup completed successfully!"
  echo "Auth state saved to: e2e/.auth/user.json"
else
  echo "Failed to create authentication state. Please check the logs above for errors."
fi

# Kill the server if we started it
if [ "$KILL_SERVER" = true ]; then
  echo "Stopping development server..."
  kill $SERVER_PID 2>/dev/null
fi
