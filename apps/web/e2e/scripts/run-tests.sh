#!/bin/bash

# Create the auth directory if it doesn't exist
mkdir -p e2e/.auth

# Check if auth file exists, if not run the setup
if [ ! -s "e2e/.auth/user.json" ]; then
  echo "Authentication state not found. Running setup..."
  ./setup-auth.sh

  # Check if setup was successful
  if [ ! -s "e2e/.auth/user.json" ]; then
    echo "Failed to set up authentication. Please run 'bun run test:e2e:setup-auth' manually."
    exit 1
  fi
fi

# Run the tests
bunx playwright test "$@"

# Show the report
bunx playwright show-report
