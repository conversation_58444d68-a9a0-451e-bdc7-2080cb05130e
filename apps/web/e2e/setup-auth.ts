import fs from "fs"
import path from "path"
import { logger } from "@k22i/lib"
import { chromium } from "@playwright/test"

/**
 * This script sets up authentication for Playwright tests.
 * It logs in with test credentials and saves the authentication state.
 */
async function setupAuth() {
    logger.info("Setting up authentication for Playwright tests...")

    // Create the auth directory if it doesn't exist
    const authDir = path.join(process.cwd(), "e2e", ".auth")
    const authFile = path.join(authDir, "user.json")

    logger.info(`Ensuring auth directory exists: ${authDir}`)
    fs.mkdirSync(authDir, { recursive: true })

    // Check if we should run in headless mode (default: true)
    const headless = process.env["HEADLESS"] !== "false"
    const browser = await chromium.launch({ headless })
    const context = await browser.newContext()
    const page = await context.newPage()

    // Login
    await page.goto("http://localhost:3333/login")

    await page.locator('input[name="email"]').fill("<EMAIL>")
    await page.locator('input[type="password"]').fill("<EMAIL>")
    await page.getByRole("button", { name: "Login" }).click()

    // Wait for login to complete
    try {
        await page.waitForURL("**/*", { timeout: 10000 })
        await page.waitForTimeout(2000) // Additional wait to ensure all state is saved

        // Save storage state
        await context.storageState({ path: authFile })
    } catch (error) {
        logger.error("Error during login:", error)
    } finally {
        // Close browser
        await browser.close()
    }
}

// Run the setup
setupAuth().catch((error) => {
    logger.error("Setup failed:", error)
    process.exit(1)
})
