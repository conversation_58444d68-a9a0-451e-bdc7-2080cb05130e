# E2E Tests with <PERSON><PERSON>

This directory contains end-to-end tests using <PERSON>wright.

## Structure

- `auth/` - Authentication-related tests
- `utils/` - Test utilities and helpers
- `global-setup.ts` - Global setup for authenticated tests

## Setup Authentication

Before running the tests, you need to set up authentication. This is done automatically when you run the tests, but you can also do it manually:

```bash
# Headless mode (default, good for CI)
bun run test:e2e:setup-auth

# Non-headless mode (shows browser, good for debugging)
bun run test:e2e:setup-auth:no-headless
```

This will create the authentication state in `.auth/user.json`.

## Running Tests

To run all tests (will set up authentication if needed):

```bash
bun run test:e2e
```

To run a specific test file:

```bash
bun run test:e2e auth/login.spec.ts
```

To run tests with UI mode:

```bash
bun run test:e2e:ui
```

To view the test report:

```bash
bun run test:e2e:report
```

## Authentication

The tests use the following test credentials:

- Email: <EMAIL>
- Password: <EMAIL>

For tests that require authentication, we use <PERSON><PERSON>'s storage state feature to avoid having to log in for each test. The authentication state is stored in `.auth/user.json`.

## Test Utilities

Common test utilities are available in the `utils/` directory:

- `test-utils.ts` - Contains helper functions for login, logout, and verification
