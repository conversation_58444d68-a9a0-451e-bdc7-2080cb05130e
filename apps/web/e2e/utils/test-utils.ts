import { type Page, expect } from "@playwright/test"

/**
 * <PERSON>gin to the application with the provided credentials
 * @param page - Playwright page object
 * @param email - Email to use for login
 * @param password - Password to use for login
 */
export async function login(
    page: Page,
    email: string,
    password: string
): Promise<void> {
    // Navigate to login page
    await page.goto("/login")

    // Fill in login form
    await page.locator('input[name="email"]').fill(email)
    await page.locator('input[type="password"]').fill(password)

    // Submit the form
    await page.getByRole("button", { name: "Login" }).click()

    // Wait for navigation to complete and check for success toast
    await expect(page.getByText("Login successful")).toBeVisible({
        timeout: 5000,
    })
}

/**
 * Verify that the user is logged in by checking for elements that are only visible to authenticated users
 * @param page - Playwright page object
 */
export async function verifyLoggedIn(page: Page): Promise<void> {
    // Wait for the the home page to load
    // This assumes there's some element that's only visible when logged in
    await expect(page).toHaveURL(/app/)

    // You can add more specific checks here based on your UI
    // For example, checking for user profile elements, navigation items, etc.
}

/**
 * Logout from the application
 * @param page - Playwright page object
 */
export async function logout(_page: Page): Promise<void> {
    // This is a placeholder - implement based on your actual logout flow
    // For example:
    // await page.getByRole('button', { name: 'User menu' }).click();
    // await page.getByRole('menuitem', { name: 'Logout' }).click();
    // Wait for logout to complete
    // await expect(page).toHaveURL('/login');
}
