import { expect, test } from "@playwright/test"

// This test uses the authenticated project defined in playwright.config.ts
test.use({ storageState: "e2e/.auth/user.json" })

test.describe("Authenticated State", () => {
    test("should have access to authenticated routes without logging in", async ({
        page,
    }) => {
        // Go directly to the home page or dashboard
        await page.goto("/")

        // Verify we're not redirected to login
        await expect(page).not.toHaveURL(/login/)

        // Check for elements that should only be visible to authenticated users
        // For example:
        // await expect(page.getByRole('button', { name: 'User Profile' })).toBeVisible();
    })

    // Add more tests that require authentication
})
