import { expect, test } from "@playwright/test"
import { login, verifyLoggedIn } from "../utils/test-utils"

test.describe("Authentication", () => {
    test("should login with valid credentials", async ({ page }) => {
        // Navigate to login page
        await page.goto("/login")

        // Verify we're on the login page
        await expect(
            page.getByText("Enter your email below to login to your account")
        ).toBeVisible()

        // Fill in the login form
        await page.locator('input[name="email"]').fill("<EMAIL>")
        await page.locator('input[type="password"]').fill("<EMAIL>")

        // Click the login button
        await page.getByRole("button", { name: "Login" }).click()

        // Wait for success message
        await expect(page.getByText("Login successful")).toBeVisible({
            timeout: 5000,
        })

        // Verify we're redirected to the home page
        await expect(page).toHaveURL(/app/)

        // Additional verification that we're logged in
        // This could be checking for user-specific elements on the page
        // For example, a user profile button or user name display
    })

    test("should show error with invalid credentials", async ({ page }) => {
        // Navigate to login page
        await page.goto("/login")

        // Fill in the login form with invalid credentials
        await page.locator('input[name="email"]').fill("<EMAIL>")
        await page.locator('input[type="password"]').fill("wrongpassword")

        // Click the login button
        await page.getByRole("button", { name: "Login" }).click()

        // Wait for error message
        await expect(page.getByText(/Invalid email or password/)).toBeVisible({
            timeout: 5000,
        })

        // Verify we're still on the login page
        await expect(page).toHaveURL(/login/)
    })

    test("should login using the utility function", async ({ page }) => {
        // Use the login utility function
        await login(page, "<EMAIL>", "<EMAIL>")

        // Verify we're logged in
        await verifyLoggedIn(page)
    })
})
