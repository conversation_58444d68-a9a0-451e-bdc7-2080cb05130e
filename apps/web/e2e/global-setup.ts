import { chromium } from "@playwright/test"
import type { FullConfig } from "@playwright/test"

/**
 * Global setup function that runs before all tests.
 * This can be used to set up authentication state that can be reused across tests.
 */
async function globalSetup(config: FullConfig) {
    // Make sure we have at least one project
    if (!config.projects || config.projects.length === 0) {
        console.error("No projects found in the configuration")
        return
    }

    // Get configuration from the first project
    const project = config.projects[0]
    const use = project && project.use ? project.use : {}
    const baseURL = use.baseURL as string | undefined
    const storageState = use.storageState as string | undefined

    // Skip if no storage state file is specified
    if (!storageState) return

    // Launch browser
    const browser = await chromium.launch()
    const page = await browser.newPage({ baseURL })

    // Login
    await page.goto("/login")
    await page.locator('input[name="email"]').fill("<EMAIL>")
    await page.locator('input[type="password"]').fill("<EMAIL>")
    await page.getByRole("button", { name: "Login" }).click()

    // Wait for login to complete
    await page.waitForTimeout(2000) // Wait for any post-login processing

    // Save storage state (cookies, localStorage) to the specified file
    await page.context().storageState({ path: storageState as string })
    await browser.close()
}

export default globalSetup
