{"name": "minimal-k22i-app-web", "author": "<PERSON> <kevin<PERSON><PERSON><EMAIL>>", "description": "A production ready, batteries included starter template for Vite + React projects", "keywords": ["vite", "react", "boilerplate", "starter", "template"], "repository": {"type": "git"}, "license": "MIT", "version": "1.0.0", "type": "module", "scripts": {"setup": "git init && bunx playwright install", "format": "bunx biome format --write", "lint": "bunx biome lint --write", "check": "bunx biome check --write", "dev": "bunx --bun vite", "test": "bun test src/ && playwright test", "test:unit": "bun test src/", "test:unit:coverage": "bun test --coverage src/", "test:e2e": "./e2e/scripts/run-tests.sh", "generate": "bunx openapi-typescript http://localhost:8888/docs -o ../../packages/lib/src/schemas/index.ts && bun prettier --write ../../packages/lib/src/schemas/index.ts", "build": "bunx tsc && bunx vite build", "preview": "vite preview", "serve": "bunx serve -s dist -p 3333", "typecheck": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@k22i/lib": "workspace:*", "@k22i/ui": "workspace:*", "@tailwindcss/postcss": "^4.1.4", "@tailwindcss/vite": "^4.1.4", "@tanstack/react-query": "^5.74.4", "@tanstack/react-router": "^1.117.1", "@tanstack/react-table": "^8.21.3", "better-auth": "^1.2.7", "dayjs": "^1.11.13", "lucide-react": "^0.476.0", "next-themes": "^0.4.6", "openapi-fetch": "^0.13.5", "openapi-react-query": "^0.2.10", "react": "^19.1.0", "react-day-picker": "8.10.1", "react-dom": "^19.1.0", "react-hook-form": "^7.56.1", "serve": "^14.2.4", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.3", "zustand": "^4.5.6"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@faker-js/faker": "^8.4.1", "@headlessui/react": "^2.2.2", "@heroicons/react": "^2.2.0", "@hookform/devtools": "^4.4.0", "@playwright/test": "^1.52.0", "@tanstack/react-query-devtools": "^5.74.6", "@tanstack/react-router-devtools": "^1.120.2", "@tanstack/react-table-devtools": "^8.21.3", "@tanstack/router-plugin": "^1.117.2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@total-typescript/ts-reset": "^0.6.1", "@types/node": "^22.15.2", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react-swc": "^3.9.0", "@vitest/coverage-v8": "2.0.5", "autoprefixer": "^10.4.21", "globals": "^15.15.0", "jsdom": "^25.0.1", "openapi-typescript": "^7.6.1", "postcss": "^8.5.3", "prettier": "^3.5.3", "prop-types": "^15.8.1", "shx": "^0.3.4", "tailwindcss": "^4.1.4", "typescript": "^5.8.3", "vite": "5.4.2", "vite-plugin-static-copy": "^1.0.6", "vitest": "2.0.5"}}