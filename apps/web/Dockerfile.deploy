FROM oven/bun:1 as base
EXPOSE 3333
ENV NODE_ENV production

FROM base AS builder

WORKDIR /app

ARG VITE_API_URL
ENV VITE_API_URL=$VITE_API_URL

# Copy all workspace files first
COPY . .

# Install dependencies and build
RUN bun install --frozen-lockfile && \
    bun run build:web && \
    rm -rf node_modules && \
    bun install --frozen-lockfile --ci

FROM base AS runner

WORKDIR /app/apps/web

# Make sure the dist directory exists before copying
COPY --from=builder /app/apps/web/dist/ ./dist/
COPY --from=builder /app/apps/web/package.json ./
COPY --from=builder /app/node_modules ./node_modules

USER bun

CMD ["bun", "run", "serve"]
