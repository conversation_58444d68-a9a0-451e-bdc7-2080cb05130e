import path from "node:path"
import { fileURLToPath } from "url"
import tailwindcss from "@tailwindcss/vite"
import { TanStackRouterVite } from "@tanstack/router-plugin/vite"
import react from "@vitejs/plugin-react-swc"
import { normalizePath } from "vite"
import { viteStaticCopy } from "vite-plugin-static-copy"
import { defineConfig } from "vitest/config"

const __dirname = path.dirname(fileURLToPath(import.meta.url))
const rootDir = path.resolve(__dirname, "../../")

// https://vitejs.dev/config/
export default defineConfig({
    optimizeDeps: {
        esbuildOptions: {
            target: "esnext",
        },
    },
    build: {
        target: "esnext",
    },
    esbuild: {
        supported: {
            "top-level-await": true,
        },
    },
    plugins: [
        react(),
        tailwindcss(),
        TanStackRouterVite(),
        viteStaticCopy({
            targets: [
                {
                    src: normalizePath(path.resolve("./src/assets/locales")),
                    dest: normalizePath(path.resolve("./dist")),
                },
            ],
        }),
    ],
    resolve: {
        alias: {
            "@": path.resolve(__dirname, "./src"),
            "@@": path.resolve(__dirname, "."),
        },
    },
    server: {
        host: true,
        strictPort: true,
        port: 3333,
    },
    test: {
        environment: "jsdom",
        setupFiles: ["./vitest.setup.ts"],
        css: true,
    },
    envDir: rootDir,
})
