import path from "path"
import { defineConfig, devices } from "@playwright/test"
import dotenv from "dotenv"

/**
 * Read environment variables from file.
 * https://github.com/motdotla/dotenv
 */
dotenv.config({ path: path.resolve(process.cwd(), "../../.env") })

/**
 * See https://playwright.dev/docs/test-configuration.
 */
export default defineConfig({
    globalSetup: "./e2e/global-setup.ts",
    testDir: "./e2e",
    /* Run tests in files in parallel */
    fullyParallel: true,
    /* Fail the build on CI if you accidentally left test.only in the source code. */
    forbidOnly: !!import.meta.env["CI"],
    /* Retry on CI only */
    retries: import.meta.env["CI"] ? 2 : 0,
    /* Opt out of parallel tests on CI. */
    workers: import.meta.env["CI"] ? 1 : undefined,
    /* Reporter to use. See https://playwright.dev/docs/test-reporters */
    reporter: "html",
    /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
    use: {
        /* Base URL to use in actions like `await page.goto('/')`. */
        baseURL: import.meta.env["VITE_API_URL"]
            ? import.meta.env["ORIGIN_URL"] || "http://localhost:3333"
            : "http://localhost:3333",

        /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
        trace: "on-first-retry",
        /* Capture screenshot after each test failure */
        screenshot: "only-on-failure",
    },

    /* Configure projects for major browsers */
    projects: [
        {
            name: "chromium",
            use: {
                ...devices["Desktop Chrome"],
                // Use prepared auth state for tests that need authentication
                // storageState: "e2e/.auth/user.json",
            },
        },

        // Setup project for authenticated tests
        {
            name: "authenticated",
            use: {
                ...devices["Desktop Chrome"],
                storageState: "e2e/.auth/user.json",
            },
        },
    ],

    /* Run your local dev server before starting the tests */
    webServer: {
        command: "bun run dev",
        url: import.meta.env["ORIGIN_URL"] || "http://localhost:3333",
        reuseExistingServer: !import.meta.env["CI"],
        stdout: "pipe",
        stderr: "pipe",
        timeout: 60000, // 60 seconds timeout for server startup
    },
})
