# Playwright E2E Testing

This project uses <PERSON><PERSON> for end-to-end testing. The tests are located in the `e2e` directory.

## Setup

The tests are already configured to run with the following credentials:

- Email: <EMAIL>
- Password: <EMAIL>

## Setup Authentication

Before running the tests, you need to set up authentication. This is done automatically when you run the tests, but you can also do it manually:

```bash
# Set up authentication manually (headless mode - good for CI)
bun run test:e2e:setup-auth

# Set up authentication manually with browser visible (for debugging)
bun run test:e2e:setup-auth:no-headless
```

This will:
1. Start the development server if it's not already running
2. Create the `e2e/.auth` directory
3. Log in with the test credentials
4. Store the authentication state in `e2e/.auth/user.json`

## Running Tests

You can run the tests using the following commands:

```bash
# Run all tests (will set up authentication if needed)
bun run test:e2e

# Run tests with UI mode
bun run test:e2e:ui

# Run tests in debug mode
bun run test:e2e:debug

# View the test report
bun run test:e2e:report
```

## Test Structure

- `e2e/auth/` - Authentication-related tests
- `e2e/utils/` - Test utilities and helpers
- `e2e/global-setup.ts` - Global setup for authenticated tests

## Authentication

For tests that require authentication, we use Playwright's storage state feature to avoid having to log in for each test. The authentication state is stored in `e2e/.auth/user.json`.

## Test Utilities

Common test utilities are available in the `e2e/utils/` directory:

- `test-utils.ts` - Contains helper functions for login, logout, and verification

## Adding New Tests

To add a new test:

1. Create a new test file in the appropriate directory (e.g., `e2e/auth/new-test.spec.ts`)
2. Import the necessary utilities and helpers
3. Write your test using Playwright's API

Example:

```typescript
import { test, expect } from '@playwright/test';
import { login } from '../utils/test-utils';

test.describe('New Feature', () => {
  test('should do something', async ({ page }) => {
    // Test code here
  });

  test('should do something when authenticated', async ({ page }) => {
    // Login first
    await login(page, '<EMAIL>', '<EMAIL>');

    // Test code here
  });
});
```

## Debugging Tests

If you encounter issues with your tests, you can run them in debug mode:

```bash
bun run test:e2e:debug
```

This will open a browser window and allow you to step through the test.

## Test Results

Test results are stored in the `playwright-report` directory. You can view the report by running:

```bash
bun run test:e2e:report
```

The test results are excluded from version control by adding them to `.gitignore` and `.dockerignore`.
