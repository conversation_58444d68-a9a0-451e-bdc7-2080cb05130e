# K22i Web Application

Frontend application for the K22i platform, built with React, Vite, and shadcn/ui.

## 🚀 Quick Start

### Prerequisites

- [Bun](https://bun.sh/) - JavaScript runtime & package manager
- [Docker](https://www.docker.com/) (optional) - For containerized development

### Installation

```sh
bun install
```

### Development

```sh
bun run dev
```

The application will be available at http://localhost:3333

## 📝 Available Scripts

### Development

- `bun run dev` - Start development server
- `bun run build` - Build for production
- `bun run preview` - Preview production build locally

### Testing

- `bun run test` - Run all tests
- `bun run test:unit` - Run unit tests
- `bun run test:unit:coverage` - Run unit tests with coverage
- `bun run test:e2e` - Run end-to-end tests
- `bun run test:e2e:report` - Generate E2E test reports

### Code Quality

- `bun run lint` - Run Biome linting
- `bun run format` - Run Biome formatting
- `bun run check` - Run Biome linting and formatting

## 🔧 Development

The web application uses:

- React with Vite for fast development and optimized builds
- shadcn/ui for beautiful and accessible UI components
- TanStack Query for efficient data fetching
- TanStack Router for type-safe routing
- React Hook Form for form handling
- Zod for validation
- Tailwind CSS for styling

### 📦 Major Dependencies

#### Core

- `react` ^18.3.1 - Core React library
- `react-dom` ^18.3.1 - React DOM rendering
- `vite` 5.4.2 - Next-generation frontend tooling

#### UI Components & Styling

- `@radix-ui/*` - Collection of accessible UI primitives
- `tailwindcss` ^3.4.17 - Utility-first CSS framework
- `class-variance-authority` ^0.7.1 - Type-safe className builder
- `lucide-react` ^0.456.0 - Beautiful & consistent icons
- `sonner` ^1.7.4 - Toast notifications

#### Data Management & Routing

- `@tanstack/react-query` ^5.66.9 - Powerful data synchronization
- `@tanstack/react-router` ^1.111.11 - Type-safe routing
- `@tanstack/react-table` ^8.21.2 - Headless table management
- `zustand` ^4.5.6 - State management

#### Forms & Validation

- `react-hook-form` ^7.54.2 - Form state management
- `@hookform/resolvers` ^3.10.0 - Form validation resolvers
- `zod` ^3.24.2 - Schema validation

#### Development & Testing

- `typescript` ^5.7.3 - Type safety
- `vitest` 2.0.5 - Unit testing framework
- `@playwright/test` ^1.50.1 - E2E testing
- `biome` 1.9.4 - Linting & formatting

## 🧪 Testing

- Unit tests with Vitest and React Testing Library
- E2E tests with Playwright

## 🐳 Docker

The application includes Docker configurations for both development and production:

- `Dockerfile.dev` - Development environment
- `Dockerfile.deploy` - Production build

## 📁 Directory Structure

```
web/
├── src/                # Source code
│   ├── assets/         # Static assets (images, fonts, etc.)
│   ├── common/         # Shared types, constants, and utilities
│   ├── components/     # Reusable UI components
│   ├── features/       # Feature-specific components and logic
│   ├── hooks/          # Custom React hooks
│   ├── lib/            # Third-party library configurations
│   ├── routes/         # Route components and layouts
│   └── styles/         # Global styles and Tailwind configurations
├── e2e/                # End-to-end tests with Playwright
├── public/             # Static files served directly
└── dist/               # Production build output

Key Configuration Files:
├── vite.config.ts      # Vite configuration
├── tsconfig.json       # TypeScript configuration
├── tailwind.config.js  # Tailwind CSS configuration
├── biome.json          # Biome configuration (linting & formatting)
```

## 📚 Documentation

For more detailed information about the project structure, components, and development guidelines, please refer to the project documentation.
