{"$schema": "https://ui.shadcn.com/schema.json", "style": "new-york", "rsc": false, "tsx": true, "tailwind": {"config": "../../packages/ui/tailwind.config.ts", "css": "../../packages/ui/src/styles/globals.css", "baseColor": "gray", "cssVariables": true, "prefix": ""}, "aliases": {"components": "@/components", "hooks": "@/hooks", "lib": "@/lib", "utils": "@k22i/ui/src/lib/utils", "ui": "@k22i/ui/src/components"}, "iconLibrary": "lucide"}