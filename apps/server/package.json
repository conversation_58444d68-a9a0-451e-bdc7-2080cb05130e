{"name": "server", "type": "module", "scripts": {"dev": "bun run --hot src/index.ts", "build": "bun build --target=bun --sourcemap ./src/index.ts ./src/worker.ts --outdir ./dist", "start": "bun run dist/index.js", "format": "bunx biome format --write", "lint": "bunx biome lint --write", "check": "bunx biome check --write", "typecheck": "tsc --noEmit", "db:generate": "bun drizzle-kit generate", "db:migrate": "bun drizzle-kit migrate", "db:studio": "bun drizzle-kit studio --host=0.0.0.0 --port=7070 --verbose", "db:drop": "bun drizzle-kit drop", "db:purge": "bun run ./src/lib/db/purge.ts", "db:seed": "bun run ./scripts/seed.ts", "new-module": "bun run ./scripts/new-module.ts", "post-migration:new": "bun run ./scripts/new-post-migration-script.ts", "post-migration:run": "bun run ./src/modules/post-migration-scripts/run.ts", "test": "vitest"}, "dependencies": {"@hono/zod-openapi": "^0.19.6", "@k22i/config": "workspace:*", "@k22i/database": "workspace:*", "@k22i/lib": "workspace:*", "@scalar/hono-api-reference": "^0.5.184", "@sentry/bun": "^9.17.0", "amqplib": "^0.10.8", "better-auth": "^1.2.7", "chokidar": "^4.0.3", "dotenv": "^16.5.0", "drizzle-orm": "^0.40.1", "drizzle-zod": "^0.7.1", "hono": "^4.7.9", "hono-pino": "^0.7.2", "hono-rate-limiter": "^0.4.2", "http-status-codes": "^2.3.0", "pg": "^8.15.6", "pino": "^9.6.0", "pino-pretty": "^13.0.0", "postgres": "^3.4.5", "stoker": "^1.4.2", "zod": "^3.24.4"}, "devDependencies": {"@types/amqplib": "^0.10.7", "@biomejs/biome": "1.9.4", "@faker-js/faker": "^9.7.0", "@testcontainers/postgresql": "^10.25.0", "@total-typescript/ts-reset": "^0.6.1", "@types/bun": "latest", "@types/node": "^22.15.17", "@types/pg": "^8.15.1", "@types/pluralize": "^0.0.33", "chalk": "^5.4.1", "drizzle-kit": "^0.30.6", "pluralize": "^8.0.0", "prettier": "^3.5.3", "typescript": "^5.8.3", "vitest": "^3.1.3"}}