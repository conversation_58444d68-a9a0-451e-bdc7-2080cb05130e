/**
 * File Watcher Utility
 *
 * This utility monitors changes in the src/modules directory for handler and schema files.
 * When changes are detected in files ending with .handlers.ts or .schemas.ts, it automatically
 * triggers the web:generate command to update the web application's generated files.
 *
 * The `web:generate` command generates Open API types from the given schema.
 *
 * The watcher ignores all other file types to ensure efficient and targeted regeneration
 * of web assets only when relevant API interface files are modified.
 */

import { exec } from "child_process"
import { logger } from "@k22i/lib"
import chokidar from "chokidar"

chokidar.watch("apps/server/src/modules").on("change", (path, _stats) => {
    logger.info(`Detected change in ${path} `)
    logger.info("Running `./run.sh web:generate`")
    exec("./run.sh web:generate")
    if (path.endsWith(".models.ts")) {
        logger.info("Running `./run.sh db:generate`")
        exec("./run.sh db:generate")
    }
})
