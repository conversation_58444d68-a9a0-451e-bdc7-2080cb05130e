# K22i Server Service

Backend service for the K22i application, built with <PERSON><PERSON>, <PERSON><PERSON>, and Dr<PERSON>zle ORM.

## 🚀 Quick Start

### Installation

```sh
bun install
```

### Development

```sh
bun run dev
```

The API will be available at http://localhost:8888

## 📝 Available Scripts

- `bun run dev` - Start development server with hot reload
- `bun run build` - Build for production
- `bun run typecheck` - Run TypeScript type checking

### Code Quality

- `bun run format` - Format code with Biome
- `bun run lint` - Lint code with Biome
- `bun run check` - Run both formatting and linting with Biome

### Database Commands

- `bun run db:generate` - Generate new database migrations
- `bun run db:migrate` - Run pending migrations
- `bun run db:studio` - Launch Drizzle Studio
- `bun run db:purge` - Reset database schemas
- `bun run db:seed` - Seed database with initial data

### Schema Management

- `bun run schema:consolidate` - Consolidate schema files
- `bun run schema:gen` - Generate TypeScript types from schema

### Module Management

- `bun run new-module` - Create a new module with basic structure
- `bun run post-migration:new` - Create a new post-migration script
- `bun run post-migration:run` - Run pending post-migration scripts

## 🔧 Development

### 📦 Major Dependencies

#### Core Framework & Middleware

- `hono` ^4.7.2 - Fast, lightweight web framework
- `@hono/zod-openapi` ^0.16.4 - OpenAPI integration for Hono
- `@scalar/hono-api-reference` ^0.5.175 - API documentation
- `hono-rate-limiter` ^0.4.2 - Rate limiting middleware
- `better-auth` ^1.1.21 - Authentication middleware

#### Database & ORM

- `drizzle-orm` ^0.36.4 - TypeScript ORM
- `drizzle-zod` ^0.5.1 - Zod schema generator for Drizzle
- `postgres` ^3.4.5 - PostgreSQL client
- `pg` ^8.13.3 - PostgreSQL driver

#### Logging & Monitoring

- `pino` ^9.6.0 - Fast Node.js logger
- `pino-pretty` ^13.0.0 - Pretty printing for Pino logs
- `hono-pino` ^0.7.2 - Pino integration for Hono

#### Utilities

- `zod` ^3.24.2 - TypeScript-first schema validation
- `dotenv` ^16.4.7 - Environment variable management
- `http-status-codes` ^2.3.0 - HTTP status code constants

### 📁 Directory Structure

```
api/
├── src/                # Source code
│   ├── lib/            # Core libraries and configurations
│   │   ├── db/         # Database configuration and schemas
│   │   └── utils/      # Utility functions and helpers
│   ├── modules/        # Feature modules (auth, users, etc.)
│   ├── middlewares/    # Custom middleware functions
│   ├── tests/          # Test files
│   ├── app.ts          # Application setup and configuration
│   └── index.ts        # Entry point
├── scripts/            # Utility scripts for development
├── drizzle/            # Database migrations
└── dist/               # Production build output

Key Configuration Files:
├── drizzle.config.ts   # Drizzle ORM configuration
├── env.ts              # Environment variable types
├── tsconfig.json       # TypeScript configuration
└── biome.json          # Biome configuration (linting & formatting)
```

## �🐳 Docker

The service includes Docker configurations for both development and production:

- `Dockerfile.dev` - Development environment
- `Dockerfile.deploy` - Production build
