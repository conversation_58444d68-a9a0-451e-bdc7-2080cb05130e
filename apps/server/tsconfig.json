{
    "compilerOptions": {
        "lib": ["ESNext", "DOM"],
        "target": "ESNext",
        "module": "ESNext",
        "moduleDetection": "force",
        "jsx": "react-jsx",
        "allowJs": true,

        // Bundler mode
        "moduleResolution": "bundler",
        "allowImportingTsExtensions": true,
        "verbatimModuleSyntax": true,
        "noEmit": true,

        // Best practices
        "strict": true,
        "skipLibCheck": true,
        "noFallthroughCasesInSwitch": true,

        "rootDir": ".",
        "baseUrl": ".",
        "types": ["node", "bun"],
        "declaration": false,
        "paths": {
            "@/*": ["./src/*"],
            "@@/*": ["./*"]
        }
    }
}
