import { auth } from "@/lib/auth"
import { user } from "@/modules/auth/auth.models"
import { db } from "@k22i/database"
import type { User } from "@k22i/lib/schemas/models"

interface UserWithSession extends User {
    cookie: string
}

export interface Fixtures {
    testUser: UserWithSession
}

const testUserEmail = "<EMAIL>"
const testUserPassword = "test-password123!"

/**
 * Setup a test user fixture.
 */
const setupTestUserFixture = async (
    // biome-ignore lint/correctness/noEmptyPattern: nothing to pass
    {},
    use: (user: UserWithSession) => Promise<void>
) => {
    const existingUser = await db.query.user.findFirst({
        where: (user, { eq }) => eq(user.email, testUserEmail),
    })
    if (existingUser) {
        const { headers } = await auth.api.signInEmail({
            returnHeaders: true,
            body: {
                email: testUserEmail,
                password: testUserPassword,
            },
        })
        const cookie = headers.get("set-cookie")
        if (!cookie) {
            throw new Error("Cookie not found")
        }
        const userWithSession: UserWithSession = {
            ...existingUser,
            cookie,
        }
        await use(userWithSession)
        return
    }
    const { headers, response } = await auth.api.signUpEmail({
        returnHeaders: true,
        body: {
            name: "Test User",
            email: testUserEmail,
            password: testUserPassword,
        },
    })
    const userDb = await db.query.user.findFirst({
        where: (user, { eq }) => eq(user.id, response.user.id),
    })
    if (!userDb) {
        throw new Error("Test user not found")
    }

    const cookie = headers.get("set-cookie")
    if (!cookie) {
        throw new Error("Cookie not found")
    }

    const userWithSession: UserWithSession = {
        ...userDb,
        cookie,
    }

    await use(userWithSession)
    return
}

export { setupTestUserFixture }
