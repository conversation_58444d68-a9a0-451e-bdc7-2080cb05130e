/*
 * This file should contain all the services that are used in setting up the tests.
 */

import { db } from "@k22i/database"
import { sql } from "drizzle-orm"

export const truncateTables = async () => {
    const tablesResult = await db.execute(sql`
        SELECT tablename 
        FROM pg_tables 
        WHERE schemaname = 'public' AND tablename != '__drizzle_migrations'
      `)
    if (tablesResult.length > 0) {
        const tables = tablesResult
            .map((row) => `"${row.tablename}"`)
            .join(", ")
        await db.execute(
            sql.raw(`
          DO $$
          BEGIN
            EXECUTE 'TRUNCATE TABLE ${tables} RESTART IDENTITY CASCADE';
          END $$;
        `)
        )
    }
}
