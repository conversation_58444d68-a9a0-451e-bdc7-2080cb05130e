/**
 * This file is used to set up the global context for the tests.
 */

import { execSync } from "child_process"
import { logger } from "@k22i/lib"
import { PostgreSqlContainer } from "@testcontainers/postgresql"
import type { StartedPostgreSqlContainer } from "@testcontainers/postgresql"
import type { ProvidedContext } from "vitest"
import type { TestProject } from "vitest/node"

let postgresContainer: StartedPostgreSqlContainer

declare module "vitest" {
    export interface ProvidedContext {
        DATABASE_URL: string
        NEXT_PUBLIC_DOMAIN: string
    }
}

// biome-ignore lint/suspicious/noExplicitAny: <explanation>
const runMigrations = (envVars: any) => {
    logger.info("Running database migrations...")
    try {
        const cmd = "cd ../../packages/database && bun run migrate"
        execSync(cmd, {
            stdio: "inherit",
            env: {
                ...envVars,
                PATH: process.env.PATH,
            },
            // @ts-expect-error: idk
            shell: true,
        })
        logger.info("\nDatabase migrations complete")
    } catch (error) {
        logger.error("Migration failed:", error)
        throw error
    }
}

const initializeContainer = async () => {
    logger.info("Starting PostgreSQL container...")
    const container = await new PostgreSqlContainer("postgres:17").start()
    logger.info("PostgreSQL container started")
    return container
}

const setupEnvironmentVariables = (container: StartedPostgreSqlContainer) => {
    const dbConnectionUrl = container.getConnectionUri()
    const envVars = {
        ...process.env,
        DATABASE_URL: dbConnectionUrl,
        BASE_URL: "http://localhost:8888",
        ORIGIN_URL: "http://localhost:3333",
        NODE_ENV: "test",
    }
    process.env = envVars
    return envVars
}

const setupGlobalContext = (
    provide: <T extends keyof ProvidedContext & string>(
        key: T,
        value: ProvidedContext[T]
    ) => void,
    envVars: NodeJS.ProcessEnv
) => {
    provide("DATABASE_URL", envVars.DATABASE_URL!)
    provide("NEXT_PUBLIC_DOMAIN", envVars.NEXT_PUBLIC_DOMAIN!)
}

export const setup = async ({ provide }: TestProject) => {
    postgresContainer = await initializeContainer()
    const envVars = setupEnvironmentVariables(postgresContainer)
    setupGlobalContext(provide, envVars)
    await runMigrations(envVars)
    logger.info("Setup complete")
}

export const teardown = async () => {
    if (postgresContainer) {
        await postgresContainer.stop()
        logger.info("PostgreSQL container stopped.")
    }
}
