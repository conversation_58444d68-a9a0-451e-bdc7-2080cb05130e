import { logger } from "@k22i/lib"
import type { ConsumeMessage } from "amqplib"
import {
    QUEUES,
    closeRabbitMQ,
    getChannel,
    initRabbitMQ,
} from "./lib/rabbitmq/client"

/**
 * Process a hello job
 */
async function processHelloJob(message: ConsumeMessage) {
    try {
        const content = JSON.parse(message.content.toString())
        const { id, name, delayMs } = content

        logger.info({ jobId: id, name }, "Processing hello job")

        // Simulate processing time if delay is specified
        if (delayMs && delayMs > 0) {
            logger.info({ jobId: id, delayMs }, "Delaying hello job")
            await new Promise((resolve) => setTimeout(resolve, delayMs))
        }

        // Process the job
        logger.info(
            { jobId: id },
            `Hello, ${name}! Your job has been processed.`
        )

        return true
    } catch (error) {
        logger.error({ error }, "Error processing hello job")
        return false
    }
}

/**
 * Start the worker
 */
async function startWorker() {
    try {
        logger.info("Starting worker...")

        // Initialize RabbitMQ connection
        await initRabbitMQ()
        const channel = await getChannel()

        // Set up consumer for hello queue
        channel.consume(QUEUES.HELLO, async (message) => {
            if (!message) return

            try {
                const success = await processHelloJob(message)

                if (success) {
                    // Acknowledge the message
                    channel.ack(message)
                } else {
                    // Reject the message and requeue it
                    channel.nack(message, false, true)
                }
            } catch (error) {
                logger.error({ error }, "Error in hello queue consumer")
                // Reject the message and requeue it
                channel.nack(message, false, true)
            }
        })

        logger.info("Worker started successfully")

        // Handle process termination
        process.on("SIGINT", async () => {
            logger.info("Worker shutting down...")
            await closeRabbitMQ()
            process.exit(0)
        })

        process.on("SIGTERM", async () => {
            logger.info("Worker shutting down...")
            await closeRabbitMQ()
            process.exit(0)
        })
    } catch (error) {
        logger.error({ error }, "Failed to start worker")
        process.exit(1)
    }
}

// Start the worker if this file is executed directly
if (import.meta.main) {
    logger.info("Starting worker directly...")
    startWorker()
}

export default startWorker
