import { ErrorResponse } from "@/lib/schema"
import { createRoute } from "@hono/zod-openapi"
import * as HttpStatusCodes from "stoker/http-status-codes"
import { jsonContentRequired } from "stoker/openapi/helpers"
import {
    GetOrganizationMembersRequestPath,
    GetOrganizationMembersRequestQuery,
    GetOrganizationMembersResponse,
    GetUserMembershipsResponse,
    protectedRouteSchema,
} from "./auth.schemas"

const tags = ["Auth"]

export const protectedRoute = createRoute({
    path: "/protected",
    method: "get",
    tags,
    responses: {
        [HttpStatusCodes.OK]: jsonContentRequired(
            protectedRouteSchema,
            "Protected route result"
        ),
    },
})

export const getOrganizationMembers = createRoute({
    path: "/organizations/{organizationId}/members",
    method: "get",
    tags,
    request: {
        query: GetOrganizationMembersRequestQuery,
        params: GetOrganizationMembersRequestPath,
    },
    responses: {
        [HttpStatusCodes.OK]: jsonContentRequired(
            GetOrganizationMembersResponse,
            "Organization members"
        ),
        [HttpStatusCodes.NOT_FOUND]: jsonContentRequired(
            ErrorResponse,
            "Organization not found"
        ),
        [HttpStatusCodes.BAD_REQUEST]: jsonContentRequired(
            ErrorResponse,
            "Invalid cursor format"
        ),
    },
})

export const getUserMemberships = createRoute({
    path: "/users/memberships",
    method: "get",
    tags,
    responses: {
        [HttpStatusCodes.OK]: jsonContentRequired(
            GetUserMembershipsResponse,
            "User organizations"
        ),
    },
})

export type ProtectedRoute = typeof protectedRoute
export type GetOrganizationMembers = typeof getOrganizationMembers
export type GetUserMemberships = typeof getUserMemberships
