/*
 * IMPORTANT:
 * Do not remove the column names in their declarations e.g. text("name").notNull()
 * It fucks shit up, and I don't have the time to figure out why.
 */

import { baseModel } from "@k22i/database/columns"
import { relations } from "drizzle-orm"
import {
    bigint,
    boolean,
    index,
    integer,
    pgTable,
    text,
    timestamp,
    uniqueIndex,
    uuid,
} from "drizzle-orm/pg-core"

export const user = pgTable(
    "user",
    {
        ...baseModel,
        name: text("name").notNull(),
        email: text("email").notNull().unique(),
        emailVerified: boolean("email_verified").notNull(),
        image: text("image"),
        /** https://www.better-auth.com/docs/plugins/admin#roles
         * By default, there are two roles:
         * - admin: Users with the admin role have full control over other users.
         * - user: Users with the user role have no control over other users.
         * * A user can have multiple roles. Multiple roles are stored as string separated by comma (",").
         */
        role: text("role"),
        banned: boolean("banned"),
        banReason: text("ban_reason"),
        banExpires: timestamp("ban_expires"),
        customerId: text("customer_id").unique(),
    },
    (table) => [uniqueIndex().on(table.email)]
)

export const userRelations = relations(user, ({ many }) => ({
    accounts: many(account),
    sessions: many(session),
    members: many(member),
    invitations: many(invitation),
}))

export const session = pgTable(
    "session",
    {
        ...baseModel,
        expiresAt: timestamp("expires_at").notNull(),
        token: text("token").notNull(),
        ipAddress: text("ip_address"),
        userAgent: text("user_agent"),
        userId: uuid("user_id")
            .notNull()
            .references(() => user.id),
        impersonatedBy: text("impersonated_by"),
        activeOrganizationId: uuid("active_organization_id").references(
            () => organization.id
        ),
    },
    (table) => [index().on(table.userId), uniqueIndex().on(table.token)]
)

export const sessionRelations = relations(session, ({ one }) => ({
    user: one(user, {
        fields: [session.userId],
        references: [user.id],
    }),
}))

export const account = pgTable(
    "account",
    {
        ...baseModel,
        accountId: text("account_id").notNull(),
        providerId: text("provider_id").notNull(),
        userId: uuid("user_id")
            .notNull()
            .references(() => user.id),
        accessToken: text("access_token"),
        refreshToken: text("refresh_token"),
        idToken: text("id_token"),
        accessTokenExpiresAt: timestamp("access_token_expires_at"),
        refreshTokenExpiresAt: timestamp("refresh_token_expires_at"),
        scope: text("scope"),
        password: text("password"),
    },
    (table) => [index().on(table.userId)]
)

export const accountRelations = relations(account, ({ one }) => ({
    user: one(user, {
        fields: [account.userId],
        references: [user.id],
    }),
}))

export const verification = pgTable(
    "verification",
    {
        ...baseModel,
        identifier: text("identifier").notNull(),
        value: text("value").notNull(),
        expiresAt: timestamp("expires_at").notNull(),
    },
    (table) => [index().on(table.identifier)]
)

export const organization = pgTable(
    "organization",
    {
        ...baseModel,
        name: text("name").notNull(),
        slug: text("slug"),
        logo: text("logo"),
        metadata: text("metadata"),
    },
    (table) => [index().on(table.slug)]
)

export const organizationRelations = relations(organization, ({ many }) => ({
    members: many(member),
    invitations: many(invitation),
}))

export const member = pgTable(
    "member",
    {
        ...baseModel,
        organizationId: uuid("organization_id")
            .notNull()
            .references(() => organization.id),
        userId: uuid("user_id")
            .notNull()
            .references(() => user.id),
        /** https://www.better-auth.com/docs/plugins/admin#roles
         * The role of the user in the organization. It can be admin, member, or guest.
         * * A user can have multiple roles. Multiple roles are stored as string separated by comma (",").
         */
        role: text("role").notNull(),
    },
    (table) => [
        uniqueIndex().on(table.organizationId, table.userId),
        index().on(table.organizationId),
        index().on(table.userId),
    ]
)

export const memberRelations = relations(member, ({ one }) => ({
    user: one(user, {
        fields: [member.userId],
        references: [user.id],
    }),
    organization: one(organization, {
        fields: [member.organizationId],
        references: [organization.id],
    }),
}))

export const invitation = pgTable(
    "invitation",
    {
        ...baseModel,
        organizationId: uuid("organization_id")
            .notNull()
            .references(() => organization.id),
        email: text("email").notNull(),
        /** https://www.better-auth.com/docs/plugins/admin#roles
         * The role of the user in the organization. It can be admin, member, or guest.
         * * A user can have multiple roles. Multiple roles are stored as string separated by comma (",").
         */
        role: text("role"),
        status: text("status").notNull(),
        expiresAt: timestamp("expires_at").notNull(),
        inviterId: uuid("inviter_id")
            .notNull()
            .references(() => user.id),
    },
    (table) => [
        uniqueIndex().on(table.organizationId, table.email),
        index().on(table.organizationId),
        index().on(table.inviterId),
    ]
)

export const invitationRelations = relations(invitation, ({ one }) => ({
    organization: one(organization, {
        fields: [invitation.organizationId],
        references: [organization.id],
    }),
    inviter: one(user, {
        fields: [invitation.inviterId],
        references: [user.id],
    }),
}))

export const rateLimit = pgTable("rate_limit", {
    ...baseModel,
    key: text("key"),
    count: integer("count"),
    lastRequest: bigint("last_request", { mode: "number" }),
})
