import { generateCursor } from "@/lib/pagination"
import type { AppRout<PERSON><PERSON><PERSON><PERSON> } from "@/lib/types"
import { db } from "@k22i/database"
import { eq, getTableColumns } from "drizzle-orm"
import { and } from "drizzle-orm"
import * as HttpStatusCodes from "stoker/http-status-codes"
import {
    createOrganizationMembersCursorConfig,
    createValidCursorPayloadForOrganizationMembers,
} from "./auth.helpers"
import { member, user } from "./auth.models"
import type {
    GetOrganizationMembers,
    GetUserMemberships,
    ProtectedRoute,
} from "./auth.routes"

export const protectedRoute: AppRouteHandler<ProtectedRoute> = (c) => {
    return c.json({ message: "Access to protected route granted." })
}

export const getOrganizationMembers: AppRouteHandler<
    GetOrganizationMembers
> = async (c) => {
    const { cursor, column, limit } = c.req.valid("query")
    const { organizationId } = c.req.valid("param")

    const userMemberships = await db.query.member.findMany({
        where: eq(member.organizationId, organizationId),
    })

    if (
        !userMemberships.some(
            (membership) => membership.organizationId === organizationId
        )
    ) {
        return c.json(
            {
                error: null,
                message: "Organization not found",
            },
            HttpStatusCodes.NOT_FOUND
        )
    }

    const cursorConfig = createOrganizationMembersCursorConfig(column)
    const validatedCursorPayload =
        createValidCursorPayloadForOrganizationMembers(cursor)

    const pageCursor = generateCursor(cursorConfig)
    const organizationMembers = await db
        .select({
            ...getTableColumns(user),
            joinedAt: member.createdAt,
        })
        .from(member)
        .innerJoin(user, eq(member.userId, user.id))
        .orderBy(...pageCursor.orderBy)
        .where(
            and(
                eq(member.organizationId, organizationId),
                pageCursor.where(validatedCursorPayload)
            )
        )
        // Fetch one extra to check if there's a next page
        .limit(limit + 1)

    const slicedMembers = organizationMembers.slice(0, limit)

    return c.json(
        {
            data: slicedMembers,
            cursor:
                organizationMembers.length > limit
                    ? pageCursor.serialize(slicedMembers.at(-1))
                    : null,
        },
        HttpStatusCodes.OK
    )
}

export const getUserMemberships: AppRouteHandler<GetUserMemberships> = async (
    c
) => {
    const user = c.get("user")
    const memberships = await db.query.member.findMany({
        columns: {
            id: true,
            role: true,
        },
        with: {
            organization: {
                columns: {
                    id: true,
                    name: true,
                    slug: true,
                    logo: true,
                },
            },
        },
        where: eq(member.userId, user.id),
        orderBy: (member, { asc }) => [asc(member.createdAt)],
    })

    return c.json(memberships, HttpStatusCodes.OK)
}
