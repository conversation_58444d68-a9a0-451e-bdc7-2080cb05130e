import { test } from "@/tests"
import { tClient } from "@/tests/client"
import { describe, expect } from "vitest"

describe("Auth", () => {
    test("user should be defined", async ({ testUser }) => {
        expect(testUser).toBeDefined()
    })

    describe("protected route", () => {
        test("should return 401 without auth", async () => {
            const response = await tClient.protected.$get()
            expect(response.status).toBe(401)
        })
        test("should return 200 with auth", async ({ testUser }) => {
            const response = await tClient.protected.$get(
                {},
                {
                    headers: {
                        cookie: testUser.cookie,
                    },
                }
            )
            expect(response.status).toBe(200)
        })
    })
})
