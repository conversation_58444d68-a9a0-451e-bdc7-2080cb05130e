import { encoder, serializer } from "@/lib/pagination"
import {
    PaginatedRequestSchema,
    paginatedResponse,
} from "@/lib/schema/pagination"
import { z } from "@hono/zod-openapi"
import {
    MemberSchema,
    OrganizationSchema,
    UserSchema,
} from "@k22i/lib/schemas/models"

export const protectedRouteSchema = z.object({
    message: z.string(),
})

export const GetOrganizationMembersRequestPath = z.object({
    organizationId: z.string().uuid(),
})

export const GetOrganizationMembersRequestQuery = PaginatedRequestSchema

export const getOrganizationMembersCursorConfig = {
    cursors: [{ key: "email", order: "ASC" }],
    primaryCursor: { key: "id", order: "ASC" },
} as const

export const memberCursorConfig = {
    memberCreatedAt: "member.createdAt",
    userEmail: "user.email",
} as const

export const ColumnConfigItemSchema = z.object({
    key: z.enum([
        memberCursorConfig.memberCreatedAt,
        memberCursorConfig.userEmail,
    ]),
    order: z.enum(["ASC", "DESC"]),
})

export const OrganizationMembersColumnSchema = z.array(ColumnConfigItemSchema)

export type ColumnConfigItem = z.infer<typeof ColumnConfigItemSchema>

export type OrganizationMembersColumn = z.infer<
    typeof OrganizationMembersColumnSchema
>

export const generateOrganizationMembersColumnParam = (
    columns: Array<{
        key: "member.createdAt" | "user.email"
        order: "ASC" | "DESC"
    }> = [
        { key: "member.createdAt", order: "ASC" },
        { key: "user.email", order: "ASC" },
    ]
) => {
    // Validate the columns using the schema
    const columnConfig = OrganizationMembersColumnSchema.parse(columns)

    return encoder(serializer(columnConfig))
}

export const generateSimpleColumnParam = (
    key: "member.createdAt" | "user.email" = "member.createdAt",
    order: "ASC" | "DESC" = "ASC"
) => {
    return generateOrganizationMembersColumnParam([{ key, order }])
}

export const GetOrganizationMembersResponse = paginatedResponse(
    UserSchema.extend({
        joinedAt: z.string().describe("Date the user joined the organization"),
    })
)

export const GetUserMembershipsResponse = MemberSchema.pick({
    id: true,
    role: true,
})
    .extend({
        organization: OrganizationSchema.pick({
            id: true,
            name: true,
            slug: true,
            logo: true,
        }),
    })
    .array()
