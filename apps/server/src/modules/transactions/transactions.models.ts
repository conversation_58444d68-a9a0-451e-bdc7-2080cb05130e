import { baseModel } from "@k22i/database/columns"
import { relations } from "drizzle-orm"
import {
    bigint,
    index,
    pgTable,
    text,
    timestamp,
    uuid,
} from "drizzle-orm/pg-core"
import { organization, user } from "../auth/auth.models"
import { category } from "../categories/categories.models"
import { currency } from "../currencies/currencies.models"
import { transactionTagAssociation } from "../tags/tags.models"
import { wallet } from "../wallets/wallets.models"

export const transaction = pgTable(
    "transaction",
    {
        ...baseModel,
        transactionDate: timestamp().notNull(),
        description: text().notNull(),
        amount: bigint({
            mode: "bigint",
        }).notNull(),
        currencyId: uuid()
            .notNull()
            .references(() => currency.id),
        categoryId: uuid()
            .notNull()
            .references(() => category.id),
        userId: uuid()
            .notNull()
            .references(() => user.id),
        organizationId: uuid()
            .notNull()
            .references(() => organization.id),
        sourceWalletId: uuid().references(() => wallet.id),
        // For transfers
        destinationWalletId: uuid().references(() => wallet.id),
        // For when transaction currency != organization default currency
        estimatedDefaultCurrencyValue: bigint({
            mode: "bigint",
        }),
        currencyExchangeRate: bigint({
            mode: "bigint",
        }),
    },
    (table) => [
        index().on(table.userId),
        index().on(table.organizationId),
        index().on(table.sourceWalletId),
        index().on(table.destinationWalletId),
    ]
)

export const transactionRelations = relations(transaction, ({ one, many }) => ({
    currency: one(currency, {
        fields: [transaction.currencyId],
        references: [currency.id],
    }),
    category: one(category, {
        fields: [transaction.categoryId],
        references: [category.id],
    }),
    transactionTagAssociation: many(transactionTagAssociation),
    user: one(user, {
        fields: [transaction.userId],
        references: [user.id],
    }),
    organization: one(organization, {
        fields: [transaction.organizationId],
        references: [organization.id],
    }),
    sourceWallet: one(wallet, {
        fields: [transaction.sourceWalletId],
        references: [wallet.id],
        relationName: "sourceWallet",
    }),
    destinationWallet: one(wallet, {
        fields: [transaction.destinationWalletId],
        references: [wallet.id],
        relationName: "destinationWallet",
    }),
}))
