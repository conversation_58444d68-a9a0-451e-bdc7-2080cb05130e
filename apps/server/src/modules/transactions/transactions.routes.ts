import { createRoute } from "@hono/zod-openapi"
import * as HttpStatusCodes from "stoker/http-status-codes"
import { jsonContentRequired } from "stoker/openapi/helpers"
import {
    GetTransactionsRequestQuery,
    GetTransactionsResponse,
} from "./transactions.schemas"

const tags = ["Transactions"]

export const getTransactions = createRoute({
    path: "/",
    method: "get",
    tags,
    request: {
        query: GetTransactionsRequestQuery,
    },
    responses: {
        [HttpStatusCodes.OK]: jsonContentRequired(
            GetTransactionsResponse,
            "Transactions"
        ),
    },
})

export type GetTransactionsRoute = typeof getTransactions
