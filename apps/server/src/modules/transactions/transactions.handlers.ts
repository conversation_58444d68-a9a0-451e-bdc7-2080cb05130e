import type { App<PERSON>oute<PERSON><PERSON><PERSON> } from "@/lib/types"
import { db } from "@k22i/database"
import { transaction } from "./transactions.models"
import type { GetTransactionsRoute } from "./transactions.routes"

export const getTransactions: AppRouteHandler<GetTransactionsRoute> = async (
    c
) => {
    const transactions = await db.select().from(transaction)
    return c.json(transactions)
}
