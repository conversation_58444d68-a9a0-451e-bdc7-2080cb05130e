import { createRoute } from "@hono/zod-openapi"
import * as HttpStatusCodes from "stoker/http-status-codes"
import { jsonContentRequired } from "stoker/openapi/helpers"
import {
    GetHouseholdsRequestQuery,
    GetHouseholdsResponse,
} from "./households.schemas"

const tags = ["Households"]

export const getHouseholds = createRoute({
    path: "/",
    method: "get",
    tags,
    request: {
        query: GetHouseholdsRequestQuery,
    },
    responses: {
        [HttpStatusCodes.OK]: jsonContentRequired(
            GetHouseholdsResponse,
            "Households"
        ),
    },
})

export type GetHouseholdsRoute = typeof getHouseholds
