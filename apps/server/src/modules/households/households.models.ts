import { baseModel } from "@k22i/database/columns"
import { pgTable, uuid } from "drizzle-orm/pg-core"
import { organization } from "../auth/auth.models"
import { currency } from "../currencies/currencies.models"

export const householdSetting = pgTable("household_settings", {
    ...baseModel,
    householdId: uuid()
        .notNull()
        .references(() => organization.id),
    defaultCurrencyId: uuid()
        .notNull()
        .references(() => currency.id),
})
