import { createRoute } from "@hono/zod-openapi"
import * as HttpStatusCodes from "stoker/http-status-codes"
import { jsonContentRequired } from "stoker/openapi/helpers"
import { GetCurrenciesResponse } from "./currencies.schemas"

const tags = ["Currencies"]

export const getCurrencies = createRoute({
    path: "/",
    method: "get",
    tags,
    responses: {
        [HttpStatusCodes.OK]: jsonContentRequired(
            GetCurrenciesResponse,
            "Currencies"
        ),
    },
})

export type GetCurrenciesRoute = typeof getCurrencies
