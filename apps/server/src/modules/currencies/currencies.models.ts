import { baseModel } from "@k22i/database/columns"
import { relations } from "drizzle-orm"
import { integer, pgTable, text } from "drizzle-orm/pg-core"
import { transaction } from "../transactions/transactions.models"

export const currency = pgTable("currency", {
    ...baseModel,
    symbol: text().notNull(),
    name: text().notNull(),
    symbolNative: text().notNull(),
    decimalDigits: integer().notNull(),
    code: text().notNull(),
    namePlural: text().notNull(),
})

export const currencyRelations = relations(currency, ({ many }) => ({
    transactions: many(transaction),
}))
