import type { App<PERSON>oute<PERSON><PERSON><PERSON> } from "@/lib/types"
import { db } from "@k22i/database"
import { currency } from "./currencies.models"
import type { GetCurrenciesRoute } from "./currencies.routes"

export const getCurrencies: AppRouteHandler<GetCurrenciesRoute> = async (c) => {
    const currencies = await db
        .select({
            id: currency.id,
            symbol: currency.symbol,
            name: currency.name,
            code: currency.code,
        })
        .from(currency)
    return c.json(currencies)
}
