import { createRoute } from "@hono/zod-openapi"
import * as HttpStatusCodes from "stoker/http-status-codes"
import { jsonContentRequired } from "stoker/openapi/helpers"
import { GetTagsRequestQuery, GetTagsResponse } from "./tags.schemas"

const tags = ["Tags"]

export const getTags = createRoute({
    path: "/",
    method: "get",
    tags,
    request: {
        query: GetTagsRequestQuery,
    },
    responses: {
        [HttpStatusCodes.OK]: jsonContentRequired(GetTagsResponse, "Tags"),
    },
})

export type GetTagsRoute = typeof getTags
