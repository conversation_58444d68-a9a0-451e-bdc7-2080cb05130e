import { baseModel } from "@k22i/database/columns"
import { relations } from "drizzle-orm"
import { index, pgTable, text, uniqueIndex, uuid } from "drizzle-orm/pg-core"
import { organization } from "../auth/auth.models"
import { transaction } from "../transactions/transactions.models"
import { colorEnum } from "../wallets/wallets.models"

export const tag = pgTable("tag", {
    ...baseModel,
    name: text().notNull(),
    organizationId: uuid()
        .notNull()
        .references(() => organization.id),
    color: colorEnum().notNull(),
})

export const transactionTagAssociation = pgTable(
    "transaction_tag_association",
    {
        transactionId: uuid()
            .notNull()
            .references(() => transaction.id),
        tagId: uuid()
            .notNull()
            .references(() => tag.id),
    },
    (table) => [
        uniqueIndex().on(table.transactionId, table.tagId),
        index().on(table.transactionId),
        index().on(table.tagId),
    ]
)

export const tagRelations = relations(tag, ({ many }) => ({
    transactionTagAssociation: many(transactionTagAssociation),
}))

export const transactionTagAssociationRelations = relations(
    transactionTagAssociation,
    ({ one }) => ({
        transaction: one(transaction, {
            fields: [transactionTagAssociation.transactionId],
            references: [transaction.id],
        }),
        tag: one(tag, {
            fields: [transactionTagAssociation.tagId],
            references: [tag.id],
        }),
    })
)
