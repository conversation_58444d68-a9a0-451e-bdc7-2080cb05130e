import { createRoute } from "@hono/zod-openapi"
import * as HttpStatusCodes from "stoker/http-status-codes"
import { jsonContent } from "stoker/openapi/helpers"
import { createMessageObjectSchema } from "stoker/openapi/schemas"

import { createRouter } from "@/lib/configuration/create-app"

const router = createRouter().openapi(
    createRoute({
        tags: ["Index"],
        method: "get",
        path: "/",
        responses: {
            [HttpStatusCodes.OK]: jsonContent(
                createMessageObjectSchema("minimal k22i server"),
                "minimal k22i server"
            ),
        },
    }),
    (c) => {
        return c.json(
            {
                message: "OK",
            },
            HttpStatusCodes.OK
        )
    }
)

export default router
