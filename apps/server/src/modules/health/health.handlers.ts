import type { App<PERSON>outeH<PERSON><PERSON> } from "@/lib/types"
import type { HealthCheckRoute } from "./health.routes"

export const healthCheck: AppRouteHandler<HealthCheckRoute> = async (c) => {
    let dbHealth: "healthy" | "dead" = "healthy"
    try {
        // Use the global database instance
        const { db } = await import("@k22i/database")
        const { sql } = await import("drizzle-orm")
        await db.execute(sql`SELECT 1`)
    } catch (_error) {
        dbHealth = "dead"
    }
    return c.json({
        server: "healthy",
        db: dbHealth,
    })
}
