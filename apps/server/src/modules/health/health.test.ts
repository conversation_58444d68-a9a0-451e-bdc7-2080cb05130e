import { tClient } from "@/tests/client"
import { describe, expect, test } from "vitest"

describe("Health", () => {
    test("should return 200", async () => {
        const response = await tClient.health.$get()
        expect(response.status).toBe(200)
        const json = await response.json()
        expect(json).toMatchObject({
            server: "healthy",
            db: "healthy",
        })
    })
})
