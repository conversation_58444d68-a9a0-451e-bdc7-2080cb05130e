import { createRoute } from "@hono/zod-openapi"
import * as HttpStatusCodes from "stoker/http-status-codes"
import { jsonContentRequired } from "stoker/openapi/helpers"
import { healthCheckSchema } from "./health.schemas"

const tags = ["Health"]

export const healthCheck = createRoute({
    path: "/health",
    method: "get",
    tags,
    responses: {
        [HttpStatusCodes.OK]: jsonContentRequired(
            healthCheckSchema,
            "Health check result"
        ),
    },
})

export type HealthCheckRoute = typeof healthCheck
