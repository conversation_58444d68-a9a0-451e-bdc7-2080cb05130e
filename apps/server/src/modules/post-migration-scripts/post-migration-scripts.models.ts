import { sql } from "drizzle-orm"
import { pgEnum, pgTable, text, timestamp } from "drizzle-orm/pg-core"

export const postMigrationScriptStatusEnum = pgEnum(
    "post_migration_script_state",
    ["pending", "success", "failed"]
)

export const postMigrationScript = pgTable("post_migration_script", {
    id: text().primaryKey().default(sql`gen_random_uuid()`),
    fileName: text().notNull(),
    createdAt: timestamp().notNull().defaultNow(),
    completedAt: timestamp(),
    errorMessage: text(),
    status: postMigrationScriptStatusEnum().notNull().default("pending"),
})
