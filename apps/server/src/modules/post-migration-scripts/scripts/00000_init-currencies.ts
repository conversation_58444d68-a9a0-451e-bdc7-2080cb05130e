import { currency } from "@/modules/currencies/currencies.models"
import { toCamelCase } from "@/utils/casing"
import { db } from "@k22i/database"
import { logger } from "@k22i/lib"
import { CurrencyInsertSchema } from "@k22i/lib/schemas/models"
import { z } from "zod"

// Define the schema for the incoming snake_case JSON
const SnakeCaseCurrency = z.object({
    symbol: z.string(),
    name: z.string(),
    symbol_native: z.string(),
    decimal_digits: z.number(),
    rounding: z.number(),
    code: z.string(),
    name_plural: z.string(),
})

/**
 * Converts snake_case keys to camelCase
 * @param obj The object with snake_case keys
 * @returns The same object with camelCase keys
 */
const snakeToCamel = (
    obj: Record<string, unknown>
): Record<string, unknown> => {
    const result: Record<string, unknown> = {}

    for (const key in obj) {
        const camelKey = toCamelCase(key)
        result[camelKey] = obj[key]
    }

    return result
}

const fileName = __filename.split("/").pop()

const run = async () => {
    logger.info(`Running script: ${fileName}`)

    /* -------------------------------------------------------------------------- */
    /*                           Code your script below                           */
    /* -------------------------------------------------------------------------- */
    const res = await fetch(
        "https://gist.githubusercontent.com/kmallari/0b1abe7cbe9aeebbf0852ab03369f796/raw/c41b00a83bc4bb7adf2ae22319ca7c4412443a8a/gistfile1.txt"
    )
    const json = await res.json()

    const currencies = Object.values(json).map((currencyData) => {
        const validatedData = SnakeCaseCurrency.parse(currencyData)
        const camelCaseData = snakeToCamel(validatedData)
        return CurrencyInsertSchema.parse(camelCaseData)
    })

    await db.insert(currency).values(currencies)
}

export default run
