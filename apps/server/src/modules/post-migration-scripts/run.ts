/*
 * ! IMPORTANT !
 * This should should only be ran using the `./run.sh` file at the root of this project.
 * This script will not run correctly otherwise.
 */

import { join } from "path"
import { db } from "@k22i/database"
import { logger } from "@k22i/lib"
import { Glob } from "bun"
import { eq } from "drizzle-orm"
import { postMigrationScript } from "./post-migration-scripts.models"

const run = async () => {
    const scriptsPath = join(
        process.cwd(),
        "src/modules/post-migration-scripts/scripts/**/*.ts"
    )
    const scriptsGlob = new Glob(scriptsPath)
    const scriptFiles = await Array.fromAsync(scriptsGlob.scan())

    if (scriptFiles.length === 0) {
        logger.info(
            "No post migration script files found in directory. Exiting..."
        )
        return
    }

    const postMigrationScripts = await db.query.postMigrationScript.findMany()
    const postMigrationScriptsSet = new Set(
        postMigrationScripts.map((script) => script.fileName)
    )
    const scriptFileNames = scriptFiles.map((file) => file.split("/").pop())
    const scriptFileNamesSet = new Set(scriptFileNames)

    const scriptsToRunSet = scriptFileNamesSet.difference(
        postMigrationScriptsSet
    )

    if (scriptsToRunSet.size === 0) {
        logger.info("No post migration scripts to run. Exiting...")
        return
    }
    logger.info({
        msg: "Found post migration script/s to run.",
        count: scriptsToRunSet.size,
    })

    const scriptsToRun = Array.from(scriptsToRunSet)

    for (const scriptFileName of scriptsToRun) {
        const scriptLogger = logger.child({
            scriptFileName,
        })
        if (!scriptFileName) {
            scriptLogger.warn("Script file name is undefined.")
            continue
        }
        const scriptPath = join(
            process.cwd(),
            "src/modules/post-migration-scripts/scripts",
            scriptFileName
        )

        const [pms] = await db
            .insert(postMigrationScript)
            .values({
                fileName: scriptFileName,
            })
            .returning()
        scriptLogger.info("Initializing script.")
        try {
            const script = await import(scriptPath)
            await script.default()
            await db
                .update(postMigrationScript)
                .set({
                    status: "success",
                    completedAt: new Date(),
                })
                .where(eq(postMigrationScript.id, pms.id))
            scriptLogger.info("Script executed successfully.")
        } catch (error) {
            scriptLogger.error({
                msg: "Failed to execute script.",
                error,
            })
            await db
                .update(postMigrationScript)
                .set({
                    status: "failed",
                    errorMessage: String(error),
                    completedAt: new Date(),
                })
                .where(eq(postMigrationScript.id, pms.id))
            throw error
        }
    }
}

run()
    .then(() => {
        logger.info("Post migration scripts ran successfully.")
        process.exit(0)
    })
    .catch((e) => {
        console.error(e)
        throw e
    })
