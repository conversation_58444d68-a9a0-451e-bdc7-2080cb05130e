import { createRoute } from "@hono/zod-openapi"
import * as HttpStatusCodes from "stoker/http-status-codes"
import { jsonContentRequired } from "stoker/openapi/helpers"
import {
    GetCategoriesRequestQuery,
    GetCategoriesResponse,
} from "./categories.schemas"

const tags = ["Categories"]

export const getCategories = createRoute({
    path: "/",
    method: "get",
    tags,
    request: {
        query: GetCategoriesRequestQuery,
    },
    responses: {
        [HttpStatusCodes.OK]: jsonContentRequired(
            GetCategoriesResponse,
            "Categories"
        ),
    },
})

export type GetCategoriesRoute = typeof getCategories
