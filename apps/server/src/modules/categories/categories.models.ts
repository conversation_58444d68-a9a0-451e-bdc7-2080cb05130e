import { baseModel } from "@k22i/database/columns"
import { relations } from "drizzle-orm"
import { pgTable, text, uuid } from "drizzle-orm/pg-core"
import { organization } from "../auth/auth.models"
import { transaction } from "../transactions/transactions.models"
import { colorEnum } from "../wallets/wallets.models"

export const category = pgTable("category", {
    ...baseModel,
    name: text().notNull(),
    type: text().notNull(),
    organizationId: uuid()
        .notNull()
        .references(() => organization.id),
    color: colorEnum().notNull(),
    icon: text().notNull(),
})

export const categoryRelations = relations(category, ({ many, one }) => ({
    transactions: many(transaction),
    organization: one(organization, {
        fields: [category.organizationId],
        references: [organization.id],
    }),
}))
