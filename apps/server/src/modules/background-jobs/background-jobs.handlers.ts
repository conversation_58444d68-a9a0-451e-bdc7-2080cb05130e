import { randomUUID } from "crypto"
import { QUEUES, publishMessage } from "@/lib/rabbitmq/client"
import type { AppRouteHand<PERSON> } from "@/lib/types"
import { env } from "@k22i/config/env"
import { logger } from "@k22i/lib"
import * as HttpStatusCodes from "stoker/http-status-codes"
import type { EnqueueHelloJobRoute } from "./background-jobs.routes"

export const enqueueHelloJob: AppRouteHandler<EnqueueHelloJobRoute> = async (
    c
) => {
    if (env.NODE_ENV === "production") {
        return c.json(
            {
                success: false,
                jobId: null,
                message: "Enqueuing this job is disabled in production",
            },
            HttpStatusCodes.INTERNAL_SERVER_ERROR
        )
    }
    try {
        const jobId = randomUUID()
        const body = c.req.valid("json")
        const { name, delayMs } = body
        const jobPayload = {
            id: jobId,
            name,
            delayMs: delayMs || 0,
            createdAt: new Date().toISOString(),
        }
        const success = await publishMessage(QUEUES.HELLO, jobPayload)
        if (!success) {
            logger.error({ jobPayload }, "Failed to queue hello job")
            return c.json(
                { success: false, jobId, message: "Failed to queue job" },
                HttpStatusCodes.INTERNAL_SERVER_ERROR
            )
        }
        logger.info({ jobId, name }, "Hello job queued successfully")
        return c.json(
            {
                success: true,
                jobId,
                message: `Hello job for ${name} queued successfully`,
            },
            HttpStatusCodes.CREATED
        )
    } catch (error) {
        logger.error({ error }, "Error in addHelloJobHandler")
        return c.json(
            { success: false, jobId: null, message: String(error) },
            HttpStatusCodes.INTERNAL_SERVER_ERROR
        )
    }
}
