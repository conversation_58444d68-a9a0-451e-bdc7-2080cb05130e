import { createRoute } from "@hono/zod-openapi"
import * as HttpStatusCodes from "stoker/http-status-codes"
import { jsonContentRequired } from "stoker/openapi/helpers"
import {
    EnqueueHelloJobJobsResponse,
    EnqueueHelloJobRequestPayload,
} from "./background-jobs.schemas"

const tags = ["Background Jobs"]

export const enqueueHelloJob = createRoute({
    path: "/hello",
    method: "post",
    tags,
    request: {
        body: jsonContentRequired(
            EnqueueHelloJobRequestPayload,
            "Enqueue Hello Job Request"
        ),
    },
    responses: {
        [HttpStatusCodes.CREATED]: jsonContentRequired(
            EnqueueHelloJobJobsResponse,
            "Enqueue Hello Job Success"
        ),
        [HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContentRequired(
            EnqueueHelloJobJobsResponse,
            "Enqueue Hello Job Error"
        ),
    },
})

export type EnqueueHelloJobRoute = typeof enqueue<PERSON>elloJob
