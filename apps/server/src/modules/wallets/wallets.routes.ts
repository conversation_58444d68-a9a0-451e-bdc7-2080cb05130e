import { createRoute } from "@hono/zod-openapi"
import * as HttpStatusCodes from "stoker/http-status-codes"
import { jsonContentRequired } from "stoker/openapi/helpers"
import { GetWalletsRequestQuery, GetWalletsResponse } from "./wallets.schemas"

const tags = ["Wallets"]

export const getWallets = createRoute({
    path: "/",
    method: "get",
    tags,
    request: {
        query: GetWalletsRequestQuery,
    },
    responses: {
        [HttpStatusCodes.OK]: jsonContentRequired(
            GetWalletsResponse,
            "Wallets"
        ),
    },
})

export type GetWalletsRoute = typeof getWallets
