import { baseModel } from "@k22i/database/columns"
import { relations } from "drizzle-orm"
import { bigint, pgEnum, pgTable, text, uuid } from "drizzle-orm/pg-core"
import { organization } from "../auth/auth.models"
import { transaction } from "../transactions/transactions.models"

export const colorEnum = pgEnum("color", [
    "slate",
    "gray",
    "zinc",
    "neutral",
    "stone",
    "red",
    "orange",
    "amber",
    "yellow",
    "lime",
    "green",
    "emerald",
    "teal",
    "cyan",
    "sky",
    "blue",
    "indigo",
    "violet",
    "purple",
    "fuchsia",
    "pink",
    "rose",
])

export const walletGroupTypeEnum = pgEnum("wallet_group_type", [
    "asset",
    "debt",
])

export const walletGroup = pgTable("wallet_group", {
    ...baseModel,
    name: text().notNull(),
    type: walletGroupTypeEnum().notNull(),
    organizationId: uuid()
        .notNull()
        .references(() => organization.id),
})

export const walletGroupRelations = relations(walletGroup, ({ many }) => ({
    wallets: many(wallet),
}))

export const wallet = pgTable("wallet", {
    ...baseModel,
    name: text().notNull(),
    walletGroupId: uuid()
        .notNull()
        .references(() => walletGroup.id),
    balance: bigint({ mode: "bigint" }).notNull(),
})

export const walletRelations = relations(wallet, ({ one, many }) => ({
    walletGroup: one(walletGroup, {
        fields: [wallet.walletGroupId],
        references: [walletGroup.id],
    }),
    sourceTransactions: many(transaction, {
        relationName: "sourceWallet",
    }),
    destinationTransactions: many(transaction, {
        relationName: "destinationWallet",
    }),
}))
