import {
    configureAuth,
    configureCors,
    configureCsrf,
    configureMonitoring,
    configureOpenAPI,
    createApp,
} from "@/lib/configuration"
import auth from "@/modules/auth/auth.index"
import backgroundJobs from "@/modules/background-jobs/background-jobs.index"
import currencies from "@/modules/currencies/currencies.index"
import health from "@/modules/health/health.index"
import index from "@/modules/index.route"
import { requireAuthMiddleware } from "./middlewares/auth"

const app = createApp()

configureMonitoring()
configureCors(app)
configureCsrf(app)
configureOpenAPI(app)
configureAuth(app)

const nonAuthzProtectedRoutes = [index, health, backgroundJobs] as const
const authzProtectedRoutes = [auth, currencies] as const

nonAuthzProtectedRoutes.forEach((route) => {
    app.route("/", route)
})

authzProtectedRoutes.forEach((route) => {
    route.routes.forEach((subroute) => {
        app.use(subroute.path, requireAuthMiddleware)
    })
    app.route("/", route)
})

const routes = [...authzProtectedRoutes, ...nonAuthzProtectedRoutes] as const

export type AppType = (typeof routes)[number]

export default app
