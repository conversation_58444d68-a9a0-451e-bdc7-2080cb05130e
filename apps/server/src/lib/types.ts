import type { <PERSON>AP<PERSON><PERSON><PERSON>, RouteConfig, RouteHand<PERSON> } from "@hono/zod-openapi"
import type { <PERSON><PERSON><PERSON>og<PERSON> } from "hono-pino"
import type { auth } from "./auth"

export interface AppBindings {
    Variables: {
        logger: PinoLogger
        user: typeof auth.$Infer.Session.user
        session: typeof auth.$Infer.Session.session
    }
}

export type AppOpenAPI = OpenAPIHono<AppBindings>

export type AppRouteHandler<R extends RouteConfig> = RouteHandler<
    R,
    AppBindings
>
