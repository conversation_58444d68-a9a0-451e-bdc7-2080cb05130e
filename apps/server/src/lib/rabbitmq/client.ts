import { env } from "@k22i/config/env"
import { logger } from "@k22i/lib"
import * as amqplib from "amqplib"

// Connection URL for RabbitMQ
const getRabbitMQUrl = () => {
    const {
        RABBITMQ_USER,
        RABBITMQ_PASSWORD,
        RABBITMQ_HOST,
        RABBITMQ_PORT,
        RABBITMQ_VHOST,
    } = env
    return `amqp://${RABBITMQ_USER}:${RABBITMQ_PASSWORD}@${RABBITMQ_HOST}:${RABBITMQ_PORT}${RABBITMQ_VHOST}`
}

// Default exchange name
export const DEFAULT_EXCHANGE = "k22i_exchange"

// Queue names
export const QUEUES = {
    HELLO: "hello_queue",
    NOTIFICATIONS: "notifications_queue",
}

// Singleton connection
let connection: amqplib.ChannelModel | null = null
let channel: amqplib.Channel | null = null

/**
 * Initialize RabbitMQ connection and channel
 */
export async function initRabbitMQ() {
    try {
        if (!connection) {
            logger.info("Connecting to RabbitMQ...")
            connection = await amqplib.connect(getRabbitMQUrl())

            if (connection) {
                connection.on("error", (err: unknown) => {
                    logger.error({ err }, "RabbitMQ connection error")
                    connection = null
                })

                connection.on("close", () => {
                    logger.info("RabbitMQ connection closed")
                    connection = null
                    channel = null
                })
            }

            logger.info("Connected to RabbitMQ")
        }

        if (!channel && connection) {
            channel = await connection.createChannel()

            if (channel) {
                // Create default exchange
                await channel.assertExchange(DEFAULT_EXCHANGE, "direct", {
                    durable: true,
                })

                // Create queues
                await channel.assertQueue(QUEUES.HELLO, { durable: true })
                await channel.assertQueue(QUEUES.NOTIFICATIONS, {
                    durable: true,
                })

                // Bind queues to exchange
                await channel.bindQueue(
                    QUEUES.HELLO,
                    DEFAULT_EXCHANGE,
                    QUEUES.HELLO
                )
                await channel.bindQueue(
                    QUEUES.NOTIFICATIONS,
                    DEFAULT_EXCHANGE,
                    QUEUES.NOTIFICATIONS
                )

                logger.info("RabbitMQ channel and queues initialized")
            }
        }

        return { connection, channel }
    } catch (error) {
        logger.error({ error }, "Failed to initialize RabbitMQ")
        throw error
    }
}

/**
 * Get RabbitMQ channel (creates connection if needed)
 */
export async function getChannel(): Promise<amqplib.Channel> {
    if (!channel) {
        await initRabbitMQ()
    }

    if (!channel) {
        throw new Error("Failed to get RabbitMQ channel")
    }

    return channel
}

export async function publishMessage(
    queue: string,
    message: unknown
): Promise<boolean> {
    try {
        const ch = await getChannel()
        const success = ch.publish(
            DEFAULT_EXCHANGE,
            queue,
            Buffer.from(JSON.stringify(message)),
            { persistent: true }
        )

        logger.info({ queue, message }, "Message published to RabbitMQ")
        return success
    } catch (error) {
        logger.error(
            { error, queue, message },
            "Failed to publish message to RabbitMQ"
        )
        return false
    }
}

/**
 * Close RabbitMQ connection
 */
export async function closeRabbitMQ() {
    try {
        if (channel) {
            await channel.close()
            channel = null
        }

        if (connection) {
            await connection.close()
            connection = null
        }

        logger.info("RabbitMQ connection closed")
    } catch (error) {
        logger.error({ error }, "Error closing RabbitMQ connection")
    }
}
