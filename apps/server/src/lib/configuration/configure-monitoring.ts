import { env } from "@k22i/config/env"
import { logger } from "@k22i/lib"
import * as Sentry from "@sentry/bun"

export const configureMonitoring = () => {
    if (!env.MONITORING_DSN) {
        logger.info("Monitoring is disabled")
        return
    }
    Sentry.init({
        dsn: env.MONITORING_DSN,
        attachStacktrace: true,
        environment: env.NODE_ENV,
    })
    logger.info("Monitoring setup complete")
}
