import { apiReference } from "@scalar/hono-api-reference"

import type { AppOpenAPI } from "@/lib/types"
import { env } from "@k22i/config/env"
import { logger } from "@k22i/lib"
import { VERSION } from "../constants"

export function configureOpenAPI(app: AppOpenAPI) {
    if (env.NODE_ENV === "production") {
        logger.info("OpenAPI is disabled in production")
        return
    }
    app.doc("/docs", {
        openapi: "3.1.0",
        info: {
            version: VERSION,
            title: "minimal k22i app API",
        },
    })

    app.get(
        "/reference",
        apiReference({
            theme: "default",
            layout: "modern",
            defaultHttpClient: {
                targetKey: "node",
                clientKey: "fetch",
            },
            spec: {
                url: "/docs",
            },
        })
    )
}
