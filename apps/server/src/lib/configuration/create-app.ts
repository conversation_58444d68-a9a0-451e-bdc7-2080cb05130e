import { OpenAPIHono } from "@hono/zod-openapi"
import { notFound, onError, serveEmojiFavicon } from "stoker/middlewares"
import { defaultHook } from "stoker/openapi"

import type { AppBindings, AppOpenAPI } from "@/lib/types"
import { pinoLogger } from "@/middlewares/pino-logger"
import { trimTrailingSlash } from "hono/trailing-slash"

export function createRouter() {
    return new OpenAPIHono<AppBindings>({
        strict: false,
        defaultHook,
    })
}

export function createApp() {
    const app = createRouter()
    app.use(serveEmojiFavicon("🔥"))
    app.use(trimTrailingSlash())
    app.use(pinoLogger())

    app.notFound(notFound)
    app.onError(onError)
    return app
}

export function createTestApp<R extends AppOpenAPI>(router: R) {
    return createApp().route("/", router)
}
