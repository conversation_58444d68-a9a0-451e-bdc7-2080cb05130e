import { SQL, and, asc, desc, eq, gt, lt, or } from "drizzle-orm"

import { parse as _parse } from "./parse"
import { serialize as _serialize } from "./serialize"
import type { <PERSON>ursor, CursorConfig } from "./types"
import {
    decoder as _decoder,
    encoder as _encoder,
    parser as _parser,
    serializer as _serializer,
    generateSubArrays,
} from "./utils"

/**
 * Generate a type-safe cursor for keyset pagination with proper ordering and filtering
 * @template T - The schema type for cursor key type-checking
 * @param config - Type-safe cursor configuration object
 * @param options - Optional utility overrides for encoding/decoding
 * @returns Cursor utility object with orderBy, where, parse, and serialize methods
 */
export const generateCursor = <T extends Record<string, unknown>>(
    config: CursorConfig<T>,
    options?: {
        /**
         * decoder: similar to `atob()` but compatible with UTF-8 strings
         * converts a base64 encoded string to a UTF-8 string
         * @link https://developer.mozilla.org/en-US/docs/Web/API/atob
         */
        decoder?: typeof _decoder

        /**
         * encoder: similar to `btoa()` but compatible with UTF-8 strings
         * converts a UTF-8 string to a base64 encoded string
         * @link https://developer.mozilla.org/en-US/docs/Web/API/btoa
         */
        encoder?: typeof _encoder

        /**
         * parser: similar to `JSON.parse()`
         * converts a JSON string to a JavaScript object
         * @link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/parse
         */
        parser?: typeof _parser

        /**
         * serializer: similar to `JSON.stringify()`
         * converts a JavaScript object to a JSON string
         * @link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/stringify
         */
        serializer?: typeof _serializer

        /**
         * parse:
         * takes a cursor and returns a JavaScript object
         * useful to retrive object from the FE client token
         */
        parse?: typeof _parse

        /**
         * serialize:
         * takes a JavaScript object and returns a cursor
         * useful to generate token to the FE client
         */
        serialize?: typeof _serialize
    }
) => {
    const { cursors = [], primaryCursor } = config
    const {
        decoder = _decoder,
        encoder = _encoder,
        parser = _parser,
        serializer = _serializer,
        parse = _parse,
        serialize = _serialize,
    } = options ?? {}

    const orderBy: Array<SQL> = []
    for (const { order = "ASC", schema } of [...cursors, primaryCursor]) {
        const fn = order === "ASC" ? asc : desc
        const sql = fn(schema)
        orderBy.push(sql)
    }

    return {
        orderBy,

        where: (lastPreviousItemData?: Partial<T> | string | null) => {
            if (!lastPreviousItemData) {
                return undefined
            }

            // Type assertion is necessary here because parse function doesn't know about T
            const data =
                typeof lastPreviousItemData === "string"
                    ? (parse(
                          config as CursorConfig,
                          lastPreviousItemData,
                          decoder,
                          parser
                      ) as T)
                    : lastPreviousItemData
            if (!data) {
                return undefined
            }

            const matrix = generateSubArrays([
                ...cursors,
                primaryCursor,
            ] as Cursor<T>[])

            const ors: Array<SQL> = []
            for (const posibilities of matrix) {
                const ands: Array<SQL> = []
                for (const cursor of posibilities) {
                    const lastValue = cursor === posibilities?.at(-1)
                    const { order = "ASC", schema, key } = cursor
                    const fn = order === "ASC" ? gt : lt
                    // Type assertion to ensure key can be used to index data
                    const value = data[key as keyof T]
                    const sql = !lastValue
                        ? eq(schema, value)
                        : fn(schema, value)
                    ands.push(sql)
                }
                const _and = and(...ands)
                if (!_and) {
                    continue
                }
                ors.push(_and)
            }
            const where = or(...ors)

            return where
        },

        parse: (cursor: string | null) =>
            parse(config as CursorConfig, cursor, decoder, parser) as T | null,

        serialize: (data?: T | null) =>
            serialize(
                config as CursorConfig,
                data as Record<string, unknown> | null | undefined,
                encoder,
                serializer
            ),
    }
}

export default generateCursor
