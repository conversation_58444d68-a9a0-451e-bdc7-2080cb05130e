import type { AnyColumn } from "drizzle-orm"

/**
 * Type-safe cursor configuration for keyset pagination.
 * T represents the schema type, ensuring only valid schema keys can be used.
 */
export type Cursor<
    T extends Record<string, unknown> = Record<string, AnyColumn>,
> = {
    order?: "ASC" | "DESC"
    key: keyof T
    schema: AnyColumn
}

/**
 * Configuration for cursor-based pagination with type checking for schema keys.
 * When using CursorConfig<UserSchema>, only UserSchema keys will be available in autocomplete.
 */
export type CursorConfig<
    T extends Record<string, unknown> = Record<string, AnyColumn>,
> = {
    primaryCursor: Cursor<T>
    cursors?: Array<Cursor<T>>
}
