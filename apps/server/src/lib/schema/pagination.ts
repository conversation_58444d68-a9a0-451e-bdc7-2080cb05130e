import { z } from "zod"

export const PaginatedRequestSchema = z.object({
    // when decoded, should be in the format:
    // {
    //     key: <key>,
    //     sortDirection: "ASC" | "DESC",
    // }
    cursor: z.string().optional(),
    // column is a base64 encoded string that when decoded should be of type Cursor
    // with keys for sorting and their corresponding schema fields
    column: z.string().optional(),
    limit: z.number({ coerce: true }).default(10),
})

// Define a type for pagination metadata to avoid excessive type nesting
export type PaginationMetadata = {
    cursor: string | null
}

// Define a type for paginated response to resolve deep instantiation issues
export type PaginatedResponseType<T> = {
    data: T[]
    cursor: string | null
}

// Use a simpler approach that doesn't create excessively deep types
export const paginatedResponse = <T extends z.ZodTypeAny>(dataSchema: T) => {
    // Instead of directly nesting schemas, we create a new schema
    // that avoids excessive type recursion
    return z.object({
        data: z.array(dataSchema),
        cursor: z.string().nullable(),
    }) as z.ZodType<PaginatedResponseType<z.infer<T>>>
}
