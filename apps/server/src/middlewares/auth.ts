import { auth } from "@/lib/auth"
import { createMiddleware } from "hono/factory"

export const authMiddleware = createMiddleware(async (c, next) => {
    const session = await auth.api.getSession({
        headers: c.req.raw.headers as Headers,
    })

    if (!session) {
        c.set("user", null)
        c.set("session", null)
        return next()
    }

    c.set("user", session.user)
    c.set("session", session.session)
    return next()
})

export const requireAuthMiddleware = createMiddleware(async (c, next) => {
    const session = await auth.api.getSession({
        headers: c.req.raw.headers,
    })
    if (!session) {
        return c.json({ message: "Unauthorized" }, 401)
    }
    c.set("user", session.user)
    c.set("session", session.session)
    return next()
})
