import { env } from "@k22i/config/env"
import { pino<PERSON>ogger as logger } from "hono-pino"
import pino from "pino"
import type { LoggerOptions } from "pino"

// Define standard log levels with our custom http level
const logLevels = {
    fatal: 60,
    error: 50,
    warn: 40,
    http: 35,
    info: 30,
    debug: 20,
    trace: 10,
}

// Custom serializers to handle specific data types
const customSerializers = {
    err: pino.stdSerializers.err,
    req: (req: {
        method: string
        url: string
        headers: Record<string, string>
        remoteAddress?: string
        remotePort?: number
    }) => ({
        method: req.method,
        url: req.url,
        headers: redactHeaders(req.headers),
        remoteAddress: req.remoteAddress,
        remotePort: req.remotePort,
    }),
}

function redactHeaders(headers: Record<string, string>) {
    const sensitiveHeaders = ["authorization", "x-api-key", "cookie"]
    const redactedHeaders = { ...headers }
    for (const header of sensitiveHeaders) {
        if (header in redactedHeaders) {
            redactedHeaders[header] = "[REDACTED]"
        }
    }

    return redactedHeaders
}

export function pinoLogger() {
    const developmentOptions: Partial<LoggerOptions> = {
        transport: {
            target: "pino-pretty",
            options: {
                colorize: true,
                levelFirst: true,
                translateTime: "UTC:yyyy-mm-dd'T'HH:MM:ss.l'Z'",
                ignore: "pid,hostname",
                messageFormat: "{msg} {req.method} {req.url}",
            },
        },
    }

    const productionOptions: Partial<LoggerOptions> = {
        formatters: {
            level: (label: string) => ({ level: label }),
        },
        timestamp: () => `,"time":"${new Date().toISOString()}"`,
    }

    const pinoConfig: LoggerOptions = {
        level: env.NODE_ENV === "production" ? "info" : "trace",
        customLevels: logLevels,
        useOnlyCustomLevels: false,
        serializers: customSerializers,
        redact: {
            paths: [
                "req.headers.authorization",
                "req.body.password",
                "req.body.creditCard",
            ],
            remove: true,
        },
        ...(env.NODE_ENV === "production"
            ? productionOptions
            : developmentOptions),
    }

    return logger({
        pino: pino(pinoConfig),
        http: {
            reqId: () => crypto.randomUUID(),
            responseTime: true,
        },
    })
}
