/**
 * Normalizes an array of numbers to values between -1 and 1 while preserving their relative proportions.
 * The normalization is done by dividing each point by the maximum absolute value in the array.
 * If all values are 0, returns the original array unchanged.
 *
 * @param points - Array of numbers to normalize
 * @returns Array of normalized numbers between -1 and 1
 */
export const normalizePoints = (points: number[]): number[] => {
    const maxAbsValue = Math.max(...points.map(Math.abs))
    return maxAbsValue > 0 ? points.map((point) => point / maxAbsValue) : points
}

/**
 * Converts a string from any case style to camelCase.
 * Handles the following cases:
 * - snake_case (e.g., hello_world)
 * - kebab-case (e.g., hello-world)
 * - PascalCase (e.g., HelloWorld)
 * - SCREAMING_SNAKE_CASE (e.g., HELLO_WORLD)
 * - dot.case (e.g., hello.world)
 * - space separated (e.g., hello world)
 *
 * @param str - The string to convert
 * @returns The string in camelCase format
 */
export const toCamelCase = (str: string): string => {
    // First, handle special cases where we might have mixed formats
    return (
        str
            // Split on any non-alphanumeric character
            .split(/[^a-zA-Z0-9]+/)
            // Filter out empty strings
            .filter(Boolean)
            // Convert each part to proper case
            .map((part, index) => {
                // Convert the part to lowercase first
                const lower = part.toLowerCase()
                // If it's the first word, keep it lowercase
                // Otherwise capitalize the first letter
                return index === 0
                    ? lower
                    : lower.charAt(0).toUpperCase() + lower.slice(1)
            })
            // Join the parts together
            .join("")
    )
}

/**
 * Converts a string from any case style to PascalCase.
 * Handles the following cases:
 * - snake_case (e.g., hello_world -> HelloWorld)
 * - kebab-case (e.g., hello-world -> HelloWorld)
 * - camelCase (e.g., helloWorld -> HelloWorld)
 * - SCREAMING_SNAKE_CASE (e.g., HELLO_WORLD -> HelloWorld)
 * - dot.case (e.g., hello.world -> HelloWorld)
 * - space separated (e.g., hello world -> HelloWorld)
 *
 * @param str - The string to convert
 * @returns The string in PascalCase format
 */
export const toPascalCase = (str: string): string => {
    return (
        str
            // Split on any non-alphanumeric character
            .split(/[^a-zA-Z0-9]+/)
            // Filter out empty strings
            .filter(Boolean)
            // Convert each part to proper case
            .map((part) => {
                // Convert the part to lowercase first
                const lower = part.toLowerCase()
                // Capitalize the first letter of every word
                return lower.charAt(0).toUpperCase() + lower.slice(1)
            })
            // Join the parts together
            .join("")
    )
}

/**
 * Converts a string from any case style to Title Case.
 * Handles the following cases:
 * - snake_case (e.g., hello_world -> Hello World)
 * - kebab-case (e.g., hello-world -> Hello World)
 * - camelCase (e.g., helloWorld -> Hello World)
 * - PascalCase (e.g., HelloWorld -> Hello World)
 * - SCREAMING_SNAKE_CASE (e.g., HELLO_WORLD -> Hello World)
 * - dot.case (e.g., hello.world -> Hello World)
 * - space separated (e.g., hello world -> Hello World)
 *
 * @param str - The string to convert
 * @returns The string in Title Case format
 */
export const toTitleCase = (str: string): string => {
    return (
        str
            // Split on any non-alphanumeric character
            .split(/[^a-zA-Z0-9]+/)
            // Filter out empty strings
            .filter(Boolean)
            // Convert each part to proper case
            .map((part) => {
                // Convert the part to lowercase first
                const lower = part.toLowerCase()
                // Capitalize the first letter
                return lower.charAt(0).toUpperCase() + lower.slice(1)
            })
            // Join the parts with spaces
            .join(" ")
    )
}

/**
 * Converts a string from any case style to snake_case.
 * Handles the following cases:
 * - camelCase (e.g., helloWorld -> hello_world)
 * - PascalCase (e.g., HelloWorld -> hello_world)
 * - kebab-case (e.g., hello-world -> hello_world)
 * - SCREAMING_SNAKE_CASE (e.g., HELLO_WORLD -> hello_world)
 * - dot.case (e.g., hello.world -> hello_world)
 * - space separated (e.g., hello world -> hello_world)
 *
 * @param str - The string to convert
 * @returns The string in snake_case format
 */
export const toSnakeCase = (str: string): string => {
    return (
        str
            // Split on any non-alphanumeric character
            .split(/[^a-zA-Z0-9]+/)
            // Filter out empty strings
            .filter(Boolean)
            // Convert each part to lowercase
            .map((part) => part.toLowerCase())
            // Join the parts with underscores
            .join("_")
    )
}
