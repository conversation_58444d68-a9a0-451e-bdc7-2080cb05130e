import fs from "fs"
import path from "path"
import { logger } from "@k22i/lib"
import chalk from "chalk"
import { mkdir, writeFile } from "fs/promises"

const getScriptTemplate = () => `import { db } from '@k22i/database'
import { logger } from '@k22i/lib'

const fileName = __filename.split('/').pop()

const run = async () => {
    logger.info(\`Running script: \${fileName}\`)

    /* -------------------------------------------------------------------------- */
    /*                           Code your script below                           */
    /* -------------------------------------------------------------------------- */
}

export default run
`

const createPostMigrationScript = async () => {
    const scriptName = process.argv[2]

    if (!scriptName) {
        console.error(chalk.red("Please provide a script name"))
        throw new Error("No script name provided")
    }

    const scriptsDir = path.join(
        process.cwd(),
        "apps",
        "server",
        "src",
        "modules",
        "post-migration-scripts",
        "scripts"
    )

    try {
        // Ensure scripts directory exists
        await mkdir(scriptsDir, { recursive: true })

        // Get existing scripts to determine next number
        const files = await fs.promises.readdir(scriptsDir)

        const numbers = files
            .filter((file) => file.match(/^\d{5}_.*\.ts$/))
            .map((file) => parseInt(file.split("_")[0]))
        const nextNumber = numbers.length > 0 ? Math.max(...numbers) + 1 : 0

        // Format the script number with leading zeros
        const scriptNumber = nextNumber.toString().padStart(5, "0")

        // Create kebab-case name
        const kebabName = scriptName
            .replace(/([a-z])([A-Z])/g, "$1-$2")
            .replace(/[\s_]+/g, "-")
            .toLowerCase()

        const fileName = `${scriptNumber}_${kebabName}.ts`
        const filePath = path.join(scriptsDir, fileName)

        // Write the file
        await writeFile(filePath, getScriptTemplate(), "utf-8")

        logger.info({
            msg: "Post-migration script created",
            fileName,
        })
    } catch (error) {
        console.error("Error creating post-migration script:", error)
        throw error
    }
}

createPostMigrationScript()
