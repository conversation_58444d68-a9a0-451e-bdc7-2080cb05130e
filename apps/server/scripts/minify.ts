#!/usr/bin/env bun
import { readdirSync, statSync } from "fs"
import { readFileSync, writeFileSync } from "fs"
import { join } from "path"
import { logger as baseLogger } from "@k22i/lib"
import { transform } from "@swc/core"

/**
 * Recursively finds all TypeScript and JavaScript files in a directory
 * @param dir Directory to search
 * @returns Array of file paths
 */
function findFiles(dir: string): string[] {
    const files: string[] = []

    const entries = readdirSync(dir, { withFileTypes: true })

    for (const entry of entries) {
        const fullPath = join(dir, entry.name)

        if (entry.isDirectory()) {
            // Skip node_modules directory
            if (entry.name === "node_modules") continue

            // Recursively search subdirectories
            files.push(...findFiles(fullPath))
        } else if (
            entry.isFile() &&
            (entry.name.endsWith(".ts") ||
                entry.name.endsWith(".tsx") ||
                entry.name.endsWith(".js") ||
                entry.name.endsWith(".jsx"))
        ) {
            files.push(fullPath)
        }
    }

    return files
}

/**
 * Minifies a file using SWC
 * @param filePath Path to the file to minify
 */
async function minifyFile(filePath: string): Promise<void> {
    try {
        const code = readFileSync(filePath, "utf-8")

        const output = await transform(code, {
            filename: filePath,
            minify: true,
            jsc: {
                minify: {
                    compress: true,
                    mangle: true,
                },
                parser: {
                    syntax:
                        filePath.endsWith(".ts") || filePath.endsWith(".tsx")
                            ? "typescript"
                            : "ecmascript",
                    tsx: filePath.endsWith(".tsx") || filePath.endsWith(".jsx"),
                },
                target: "es2022",
            },
        })

        writeFileSync(filePath, output.code)
    } catch (error) {
        baseLogger.error({
            message: "❌ Error minifying file.",
            filePath,
            error,
        })
    }
}

/**
 * Main function to minify all files in a directory
 */
async function minifyDirectory(directory: string): Promise<void> {
    let logger = baseLogger.child({ directory })
    logger.info("🔍 Searching for files in the given directory.")

    try {
        // Check if directory exists
        if (!statSync(directory).isDirectory()) {
            logger.error("❌ Error: Given parameter is not a directory")
            process.exit(1)
        }

        // Find all files
        const files = findFiles(directory)
        logger = logger.child({ fileCount: files.length })

        if (files.length === 0) {
            logger.info(
                "ℹ️ No TypeScript or JavaScript files found in the provided directory."
            )
            return
        }

        logger.info("🔧 Found files to minify!")

        // Minify all files
        for (const file of files) {
            await minifyFile(file)
        }

        logger.info("🚀 Successfully minified files in the provided directory.")
    } catch (error) {
        logger.error({ message: "❌ Error:", error })
        process.exit(1)
    }
}

// Get directory from command line arguments
const directory = process.argv[2]

if (!directory) {
    baseLogger.error("❌ Error: Please provide a directory path")
    baseLogger.info("Usage: bun run scripts/minify.ts <directory>")
    process.exit(1)
}

// Run the minification
minifyDirectory(directory)
