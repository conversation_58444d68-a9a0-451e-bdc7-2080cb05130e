import { randomUUID } from "crypto"
import { faker } from "@faker-js/faker"
import { env } from "@k22i/config/env"
import { db, schema } from "@k22i/database"
import { logger } from "@k22i/lib"
import type {
    MemberInsert,
    OrganizationInsert,
    UserInsert,
} from "@k22i/lib/schemas/models"
import { eq } from "drizzle-orm"

faker.seed(1999)

async function seedUser() {
    const payload = {
        email: "<EMAIL>",
        name: "K1",
        password: "<EMAIL>",
    }
    const response = await fetch(env.BASE_URL + "/api/auth/sign-up/email", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
    })
    const json = (await response.json()) as {
        user: typeof schema.user.$inferSelect
    }
    return json["user"]
}

async function seedMoreOrganizations(userId: string) {
    const organizations: Array<OrganizationInsert> = []
    const memberships: Array<MemberInsert> = []
    for (let i = 0; i < 3; i++) {
        const name = faker.company.name()
        const slug = faker.string.alphanumeric(10)
        const logo = faker.image.url()
        const organizationId = randomUUID()
        organizations.push({
            name,
            slug,
            logo,
            id: organizationId,
        })
        if (i === 0) {
            memberships.push({
                organizationId,
                userId,
                role: "admin",
            })
            continue
        }
        if (i === 1) {
            memberships.push({
                organizationId,
                userId,
                role: "member",
            })
            continue
        }
        memberships.push({
            organizationId,
            userId,
            role: "owner",
        })
    }
    await db.insert(schema.organization).values(organizations)
    await db.insert(schema.member).values(memberships)
}

async function getUserOrganization(userId: string) {
    const [{ organization }] = await db
        .select()
        .from(schema.member)
        .leftJoin(
            schema.organization,
            eq(schema.member.organizationId, schema.organization.id)
        )
        .where(eq(schema.member.userId, userId))
        .limit(1)
    if (!organization) {
        throw new Error("Organization not found")
    }
    return organization
}

async function seedUsersInOrganization(organizationId: string) {
    const users: Array<UserInsert> = []
    const memberships: Array<MemberInsert> = []
    for (let i = 0; i < 50; i++) {
        const name = faker.person.fullName()
        const email = faker.internet
            .email({
                firstName: name.split(" ")[0],
                lastName: name.split(" ")[1] || "",
            })
            .toLowerCase()
        const userId = randomUUID()
        const memberDateCreated = faker.date.past()

        users.push({
            id: userId,
            name,
            email,
            emailVerified: false,
            role: "user",
            banned: false,
        })

        memberships.push({
            organizationId,
            userId,
            role: "member",
            createdAt: memberDateCreated,
        })
    }
    await db.insert(schema.user).values(users)
    await db.insert(schema.member).values(memberships)
}

async function seedScript() {
    const user = await seedUser()
    await seedMoreOrganizations(user.id)
    const organization = await getUserOrganization(user.id)
    await seedUsersInOrganization(organization.id)
}

seedScript()
    .then(() => {
        logger.info("✅ Successfully seeded database")
        process.exit(0)
    })
    .catch((e) => {
        console.error(e)
        throw e
    })
