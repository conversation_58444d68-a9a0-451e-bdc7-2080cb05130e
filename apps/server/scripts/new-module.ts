import path from "path"
import {
    to<PERSON>amel<PERSON><PERSON>,
    toPascalCase,
    toSnakeCase,
    toTitleCase,
} from "@/utils/casing"
import { logger as baseLogger } from "@k22i/lib"
import { mkdir, readdir, writeFile } from "fs/promises"
import pluralize from "pluralize"
import { createInterface } from "readline/promises"

async function promptUser(question: string): Promise<string> {
    const rl = createInterface({
        input: process.stdin,
        output: process.stdout,
    })

    try {
        const answer = await rl.question(question)
        return answer.toLowerCase()
    } finally {
        rl.close()
    }
}

const getSchemaTemplate = (moduleName: string) => `import { z } from 'zod'

export const Get${toPascalCase(moduleName)}RequestQuery = z.object({})
export const Get${toPascalCase(moduleName)}Response = z.object({})
`

const getRoutesTemplate = (moduleName: string) => {
    return `import { createRoute } from '@hono/zod-openapi'
import * as HttpStatusCodes from 'stoker/http-status-codes'
import { jsonContentRequired } from 'stoker/openapi/helpers'
import {
    Get${toPascalCase(moduleName)}RequestQuery,
    Get${toPascalCase(moduleName)}Response,
} from './${moduleName}.schemas'

const tags = ['${toTitleCase(moduleName)}']

export const get${toPascalCase(moduleName)} = createRoute({
    path: '/',
    method: 'get',
    tags,
    request: {
        query: Get${toPascalCase(moduleName)}RequestQuery,
    },
    responses: {
        [HttpStatusCodes.OK]: jsonContentRequired(
            Get${toPascalCase(moduleName)}Response,
        '${toPascalCase(moduleName)}'),
    },
})

export type Get${toPascalCase(moduleName)}Route = typeof get${toPascalCase(moduleName)}
`
}

const getIndexTemplate = (
    moduleName: string,
    parentModules: string[] = []
) => `import * as handlers from './${moduleName}.handlers'
import * as routes from './${moduleName}.routes'
import { createRouter } from '@/lib/configuration/create-app'

const router = createRouter()${
    parentModules.length > 0
        ? `\n    .basePath('/${parentModules.join("/")}')`
        : ""
}\n    .openapi(routes.get${toPascalCase(moduleName)}, handlers.get${toPascalCase(moduleName)})

export default router
`

const getModelTemplate = (
    moduleName: string
) => `import { pgTable } from 'drizzle-orm/pg-core'
import { baseModel } from '@k22i/database/columns'

export const ${pluralize.singular(toCamelCase(moduleName))} = pgTable(
    '${pluralize.singular(toSnakeCase(moduleName))}',
    {
        ...baseModel
    }
)
`

const getHandlersTemplate = (
    moduleName: string
) => `import { ${pluralize.singular(toCamelCase(moduleName))} } from './${moduleName}.models'
import type { Get${toPascalCase(moduleName)}Route } from './${moduleName}.routes'
import { db } from '@k22i/database'
import type { AppRouteHandler } from '@/lib/types'

export const get${toPascalCase(moduleName)}: AppRouteHandler<Get${toPascalCase(moduleName)}Route> = async (c) => {
    const ${toCamelCase(moduleName)} = await db
        .select()
        .from(${pluralize.singular(toCamelCase(moduleName))})
    return c.json(${toCamelCase(moduleName)})
}
`

export const getTestTemplate = (
    moduleName: string
) => `import { test, describe, expect } from 'vitest'
import { tClient } from '@/tests/client'

describe('${toPascalCase(moduleName)}', () => {
    test('should return 200', async () => {
        // const response = await tClient["${moduleName}"].$get()
        // expect(response.status).toBe(200)
    })
})
`

async function createModule() {
    const initialModuleName = process.argv[2]
    if (!initialModuleName) {
        throw new Error("Please provide a module name")
    }

    // Handle submodule paths (e.g., "parent/child")
    const modulePath = initialModuleName.split("/")
    const lastModuleName = modulePath[modulePath.length - 1]

    // Pluralize and convert to lowercase for consistency
    const kebabCase = pluralize(lastModuleName.toLowerCase())

    // Create the full module directory path with parent modules
    const modulePathParts = modulePath.slice(0, -1).map((part) => {
        // First convert to kebab case
        const kebabPart = part
            .replace(/([a-z])([A-Z])/g, "$1-$2")
            .replace(/[_]+/g, "-") // Only replace underscores with hyphens, not 's'
            .toLowerCase()

        // Then pluralize if needed
        return pluralize(kebabPart)
    })

    const moduleDir = path.join(
        process.cwd(),
        "src",
        "modules",
        ...modulePathParts,
        kebabCase
    )

    const logger = baseLogger.child({
        module: modulePathParts.join("/").concat("/").concat(kebabCase),
    })

    try {
        // Create directory if it doesn't exist
        await mkdir(moduleDir, { recursive: true })

        // Check if any files exist in the module directory
        try {
            const existingFiles = await readdir(moduleDir)
            const hasFiles = existingFiles.some((file) => file.includes("."))

            if (hasFiles) {
                const answer = await promptUser(
                    `Module '${kebabCase}' already exists. Are you sure you want to override it? (y/N): `
                )
                if (answer.toLowerCase() !== "y") {
                    logger.info({
                        msg: "Module creation cancelled",
                    })
                    process.exit(0)
                }
                logger.info({
                    msg: "Module    will be overridden",
                })
            }
        } catch (error: unknown) {
            if (
                typeof error === "object" &&
                error !== null &&
                "code" in error &&
                error.code !== "ENOENT"
            ) {
                throw error
            }
        }

        const files = [
            {
                name: `${kebabCase}.handlers.ts`,
                content: getHandlersTemplate(kebabCase),
            },
            {
                name: `${kebabCase}.index.ts`,
                content: getIndexTemplate(
                    kebabCase,
                    modulePathParts.concat(kebabCase)
                ),
            },
            {
                name: `${kebabCase}.routes.ts`,
                content: getRoutesTemplate(kebabCase),
            },
            {
                name: `${kebabCase}.schemas.ts`,
                content: getSchemaTemplate(kebabCase),
            },
            {
                name: `${kebabCase}.models.ts`,
                content: getModelTemplate(kebabCase),
            },
            {
                name: `${kebabCase}.test.ts`,
                content: getTestTemplate(kebabCase),
            },
        ]

        for (const file of files) {
            const filePath = path.join(moduleDir, file.name)
            await writeFile(filePath, file.content, "utf-8")
        }

        logger.info({
            msg: "Module created",
        })

        process.exit(0)
    } catch (error) {
        console.error("Error creating module:", error)
        throw new Error("Error creating module")
    }
}

createModule()
