FROM oven/bun:1 AS base
EXPOSE 8888
ENV NODE_ENV=production

FROM base AS builder

WORKDIR /app

# Copy all workspace files first
COPY . .

# Clean install with frozen lockfile
RUN bun install --frozen-lockfile && \
    bun run build:server  && \
    rm -rf node_modules && \
    bun install --frozen-lockfile --ci

FROM base AS runner

WORKDIR /app/apps/server

COPY --from=builder /app/apps/server/dist ./
COPY --from=builder /app/apps/server/package.json ./
COPY --from=builder /app/node_modules ./node_modules

USER bun

CMD ["bun", "run", "index.js"]
