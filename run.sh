#!/bin/bash

# Get the directory where the script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Source all command files
source "$SCRIPT_DIR/scripts/help.sh"
source "$SCRIPT_DIR/scripts/log-formatter.sh"
source "$SCRIPT_DIR/scripts/commands/database.sh"
source "$SCRIPT_DIR/scripts/commands/web.sh"
source "$SCRIPT_DIR/scripts/commands/server.sh"
source "$SCRIPT_DIR/scripts/commands/dev.sh"

# Main command router
case "$1" in
"dev")
  dev
  ;;
"stop")
  stop
  ;;
"db:reset")
  reset_database
  ;;
"db:seed")
  seed
  ;;
"db:purge")
  purge
  ;;
"db:migrate")
  migrate
  ;;
"db:drop")
  drop
  ;;
"db:consolidate")
  consolidate
  ;;
"db:generate")
  generate_zod_schemas
  ;;
"db:migration")
  make_migration "$@"
  ;;
"web:component")
  new_web_component "$@"
  ;;
"web:generate")
  generate_open_api_schema
  ;;
"web:e2e")
  run_e2e_tests
  ;;
"server:module")
  new_module "$@"
  ;;
"server:pms:new")
  create_new_post_migration_script "$@"
  ;;
"server:pms:run")
  run_post_migration_scripts
  ;;
"package-cleanup")
  package_cleanup
  ;;
"minify")
  minify_directory "$@"
  ;;
"-h" | "--help" | "")
  print_help
  ;;
*)
  echo "Error: Unknown command '$1'"
  print_help
  exit 1
  ;;
esac
