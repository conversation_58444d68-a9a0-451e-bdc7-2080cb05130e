#!/bin/bash

# Source the log formatter
source "$SCRIPT_DIR/scripts/log-formatter.sh"

dev() {
  echo "Starting development environment..." | format_infra

  # Create named pipes for each service
  WEB_PIPE="/tmp/k22i-web-pipe"
  SERVER_PIPE="/tmp/k22i-server-pipe"
  WORKER_PIPE="/tmp/k22i-worker-pipe"
  INFRA_PIPE="/tmp/k22i-infra-pipe"

  # Remove existing pipes if they exist
  rm -f "$WEB_PIPE" "$SERVER_PIPE" "$WORKER_PIPE" "$INFRA_PIPE"

  # Create new pipes
  mkfifo "$WEB_PIPE" "$SERVER_PIPE" "$WORKER_PIPE" "$INFRA_PIPE"

  # Start formatters in the background
  cat "$WEB_PIPE" | format_web &
  WEB_FORMAT_PID=$!

  cat "$SERVER_PIPE" | format_server &
  SERVER_FORMAT_PID=$!

  cat "$WORKER_PIPE" | format_worker &
  WORKER_FORMAT_PID=$!

  cat "$INFRA_PIPE" | format_infra &
  INFRA_FORMAT_PID=$!

  # Set up trap to handle Ctrl+C and other termination signals
  trap cleanup SIGINT SIGTERM EXIT

  # Function to clean up processes and resources
  cleanup() {
    echo "Stopping all services..." | format_infra

    # Stop the file watcher
    echo "Stopping file watcher..." | format_watcher
    stop_watcher

    # Stop docker compose services
    docker compose down

    # Kill the formatters
    if [ ! -z "$WEB_FORMAT_PID" ]; then
      kill $WEB_FORMAT_PID 2>/dev/null || true
    fi

    if [ ! -z "$SERVER_FORMAT_PID" ]; then
      kill $SERVER_FORMAT_PID 2>/dev/null || true
    fi

    if [ ! -z "$WORKER_FORMAT_PID" ]; then
      kill $WORKER_FORMAT_PID 2>/dev/null || true
    fi

    if [ ! -z "$INFRA_FORMAT_PID" ]; then
      kill $INFRA_FORMAT_PID 2>/dev/null || true
    fi

    # Clean up pipes
    rm -f "$WEB_PIPE" "$SERVER_PIPE" "$WORKER_PIPE" "$INFRA_PIPE"

    # Remove the trap
    trap - SIGINT SIGTERM EXIT
  }

  # Start infrastructure containers
  echo "Starting infrastructure containers..." | format_infra
  docker compose up -d postgres rabbitmq

  echo "Waiting for infrastructure to be healthy..." | format_infra
  while ! (docker compose ps postgres | grep -q "healthy" &&
    docker compose ps rabbitmq | grep -q "healthy"); do
    sleep 1
  done

  # Start the services with logs redirected to the appropriate pipes
  echo "Starting server application..." | format_server
  bun run --cwd apps/server dev >"$SERVER_PIPE" 2>&1 &
  SERVER_PID=$!

  echo "Starting worker..." | format_worker
  docker compose up -d worker
  docker compose logs -f worker >"$WORKER_PIPE" 2>&1 &
  WORKER_LOGS_PID=$!

  echo "Starting web application..." | format_web
  bun run --cwd apps/web dev >"$WEB_PIPE" 2>&1 &
  WEB_PID=$!

  # Log other containers to the infra pipe
  docker compose logs -f postgres rabbitmq >"$INFRA_PIPE" 2>&1 &
  INFRA_LOGS_PID=$!

  # Start the file watcher for API schema changes
  echo "Starting file watcher for API schema changes..." | format_watcher
  start_watcher

  echo "All services are running. Press Ctrl+C to stop." | format_infra
  echo "Web: http://localhost:3333" | format_web
  echo "API: http://localhost:8888" | format_server
  echo "RabbitMQ Management: http://localhost:15672" | format_infra

  # Wait for any of the log processes to finish
  wait $SERVER_LOGS_PID $WORKER_LOGS_PID $WEB_LOGS_PID $INFRA_LOGS_PID
}

stop() {
  # Stop the file watcher
  echo "Stopping file watcher..." | format_server
  stop_watcher

  # Kill any running processes
  pkill -f "bun run --cwd apps/web dev" || true
  pkill -f "bun run --cwd apps/server dev" || true
  pkill -f "bun run --cwd apps/server --watch src/worker.ts" || true

  # Kill any formatters
  pkill -f "cat /tmp/k22i-web-pipe" || true
  pkill -f "cat /tmp/k22i-server-pipe" || true
  pkill -f "cat /tmp/k22i-worker-pipe" || true

  # Clean up pipes
  rm -f /tmp/k22i-web-pipe /tmp/k22i-server-pipe /tmp/k22i-worker-pipe

  # Stop all Docker containers
  echo "Stopping infrastructure containers..." | format_infra
  docker compose down
}

start_watcher() {
  bun run apps/server/watcher.js &
  WATCHER_PID=$!
}

stop_watcher() {
  if [ ! -z "$WATCHER_PID" ]; then
    kill $WATCHER_PID
  fi
}

package_cleanup() {
  echo "Cleaning up monorepo package files..."
  rm -rf node_modules
  rm -f bun.lockb
  echo "Cleaning up API package files..."
  cd apps/server
  rm -rf node_modules
  rm -f bun.lockb
  cd ../../
  echo "Cleaning up Web package files..."
  cd apps/web
  rm -rf node_modules
  rm -f bun.lockb
  cd ../../
}

minify_directory() {
  local directory="${2}"

  if [ -z "$directory" ]; then
    echo "Error: No directory specified"
    echo "Usage: ./run.sh minify <directory>"
    exit 1
  fi

  # Check if directory exists
  if [ ! -d "$directory" ]; then
    echo "Error: Directory '$directory' does not exist"
    exit 1
  fi

  echo "Minifying files in directory: $directory"
  bun run apps/server/scripts/minify.ts "$directory"
}
