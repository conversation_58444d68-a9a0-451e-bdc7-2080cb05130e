#!/bin/bash

new_web_component() {
  local component_name="${2}"
  local component_path="src/components/${component_name}.tsx"

  cd packages/ui
  bunx --bun shadcn@latest add "${@:2}"

  # Convert component name to kebab-case for the export key
  local export_key=$(echo "${component_name}" | sed 's/[A-Z]/-\L&/g' | sed 's/^-//')

  # Add the export to package.json
  # Using jq with proper variable interpolation
  export_path="./src/components/${component_name}.tsx"
  export_key_path="./${export_key}"

  jq --arg key "$export_key_path" --arg path "$export_path" '.exports[$key] = $path' package.json >package.json.tmp
  mv package.json.tmp package.json

  # Replace all instances of "src/lib/utils" with "../lib/utils" in the src directory
  # because components.json doesn't allow relative paths
  find src -type f -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's|src/lib/utils|../lib/utils|g'

  bunx biome check --write .
}

generate_open_api_schema() {
  cd apps/web
  bun run generate
}

run_e2e_tests() {
  cd apps/web
  bun run test:e2e
}
