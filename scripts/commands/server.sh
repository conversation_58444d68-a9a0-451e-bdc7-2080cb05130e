#!/bin/bash

new_module() {
  if [ -f .env ]; then
    cp .env apps/server/.env
  fi

  cd apps/server
  bun run new-module $2

  rm -f .env
}

create_new_post_migration_script() {
  bun run ./apps/server/scripts/new-post-migration-script.ts "${@:2}"
}

run_post_migration_scripts() {
  SERVER_CONTAINER=$(docker compose ps -q server)
  docker exec -t $SERVER_CONTAINER bun run ./src/modules/post-migration-scripts/run.ts
}
