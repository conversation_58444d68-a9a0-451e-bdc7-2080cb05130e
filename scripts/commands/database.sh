#!/bin/bash

purge() {
  PG_CONTAINER=$(docker compose ps -q postgres)
  if [ -z "$PG_CONTAINER" ]; then
    echo "Error: Cannot find Postgres container"
    exit 1
  fi
  echo "Resetting database schemas..."
  docker exec -t $PG_CONTAINER psql -U postgres -c "DROP SCHEMA public CASCADE;"
  docker exec -t $PG_CONTAINER psql -U postgres -c "CREATE SCHEMA public;"
  docker exec -t $PG_CONTAINER psql -U postgres -c "DROP SCHEMA drizzle CASCADE;"
  docker exec -t $PG_CONTAINER psql -U postgres -c "CREATE SCHEMA drizzle;"
}

migrate() {
  cp .env packages/database/.env
  cp .env apps/server/.env
  sed -i '' 's|^DATABASE_URL=.*|DATABASE_URL=postgres://postgres:postgres@localhost:5432/postgres|' packages/database/.env
  cd packages/database
  bun run migrate
  rm -f .env
  cd ../../apps/server
  echo ""
  bun run ./src/modules/post-migration-scripts/run.ts
  rm -f .env
  cd ../..
}

reset_database() {
  purge
  migrate
  seed
}

seed() {
  # SERVER_CONTAINER=$(docker compose ps -q server)
  # docker exec -t $SERVER_CONTAINER bun run db:seed
  bun run --cwd apps/server db:seed
}

drop() {
  cd packages/database
  bun run db:drop
}

make_migration() {
  if [ -f .env ]; then
    cp .env packages/database/.env
  fi
  cd packages/database
  bun run drizzle-kit generate "${@:2}"
  rm -f .env
}

consolidate() {
  bun run packages/database/src/scripts/duplicate-models.ts
  bun run packages/database/src/scripts/consolidate-models.ts apps/server/src/modules packages/database/src/schema/index.ts
  ./run.sh minify packages/database/src/schema
}

generate_zod_schemas() {
  consolidate
  bun run packages/database/src/scripts/generate-schemas.ts ./packages/database/src/schema/__models ./packages/lib/src/schemas/models.ts
  bun biome check --write packages/lib/src/schemas/models.ts
  ./run.sh minify packages/database/src/schema/__models
}
