#!/bin/bash

print_help() {
  cat <<EOF
Database Management Script
-------------------------

USAGE:
    ./run.sh <command> [options]

COMMANDS:
    dev              Start Postgres in Docker and run the development environment
                     using 'bun run dev' in the root directory

    Database Commands:
    db:reset         Perform a complete database reset:
                     - Purge schemas
                     - Run migrations
                     - Seed with initial data

    db:seed          Populate the database with seed data

    db:purge         Remove all database schemas and recreate them:
                     - Drops and recreates 'public' schema
                     - Drops and recreates 'drizzle' schema

    db:migrate       Run pending database migrations using drizzle-kit, as well as post-migration scripts

    db:drop          Drop a migration

    db:generate      Generate database artifacts:
                     - Consolidate schema files
                     - Generate TypeScript types from schema
                     - Generate new drizzle migrations
                     - Update schema metadata
                     All options are passed to drizzle-kit generate

    Web Commands:
    web:install      Install web dependencies using bun
    web:component    Add a new shadcn component to the web app
    web:generate     Manually generate OpenAPI schema for API integration

    Server Commands:
    server:install      Install server dependencies using bun
    server:module       Create a new module with basic structure
    server:pms:new      Create a new post-migration script
    server:pms:run      Run pending post-migration scripts

    Misc Commands:
    package-cleanup  Clean up package files and reinstall dependencies
    minify <dir>     Minify all TypeScript/JavaScript files in the specified directory

EXAMPLES:
    ./run.sh dev              # Start development environment
    ./run.sh db:reset        # Complete database reset
    ./run.sh db:generate     # Generate new migrations
    ./run.sh web:component button  # Add shadcn button component
    ./run.sh server:module auth      # Create new auth module
    ./run.sh server:pms:new user-roles  # Create post-migration script
    ./run.sh minify apps/web/src     # Minify all files in the web src directory

For more information, visit the project documentation.
EOF
}
