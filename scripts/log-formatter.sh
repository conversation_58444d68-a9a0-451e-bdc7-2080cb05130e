#!/bin/bash

# ANSI color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
GRAY='\033[0;37m'
BOLD='\033[1m'
RESET='\033[0m'

# Service colors
WEB_COLOR=$BLUE
SERVER_COLOR=$GREEN
WORKER_COLOR=$MAGENTA
POSTGRES_COLOR=$YELLOW
RABBITMQ_COLOR=$CYAN
INFRA_COLOR=$GRAY
WATCHER_COLOR=$RED

# Function to format logs with service name prefix and color
format_log() {
  local service=$1
  local color=$2
  local timestamp=$(date +"%H:%M:%S")
  
  # Read from stdin and prefix each line
  while IFS= read -r line; do
    printf "${color}${BOLD}[%s]${RESET} ${color}[%s]${RESET} %s\n" "$timestamp" "$service" "$line"
  done
}

# Specific formatters for each service
format_web() {
  format_log "WEB" "$WEB_COLOR"
}

format_server() {
  format_log "SERVER" "$SERVER_COLOR"
}

format_worker() {
  format_log "WORKER" "$WORKER_COLOR"
}

format_postgres() {
  format_log "POSTGRES" "$POSTGRES_COLOR"
}

format_rabbitmq() {
  format_log "RABBITMQ" "$RABBITMQ_COLOR"
}

format_infra() {
  format_log "INFRA" "$INFRA_COLOR"
}

format_watcher() {
  format_log "WATCHER" "$WATCHER_COLOR"
}

# If this script is run directly, show usage
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  echo "This script is meant to be sourced by other scripts."
  echo "Usage examples:"
  echo "  command | format_web"
  echo "  command | format_server"
  echo "  command | format_worker"
fi
