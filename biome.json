{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "ignore": ["**/node_modules/**", "**/dist/**", "**/build/**", "apps/web/src/routeTree.gen.ts", "packages/database/src/migrations/meta/**", "packages/database/src/schema/__models/**"]}, "formatter": {"enabled": true, "useEditorconfig": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 4, "lineEnding": "lf", "lineWidth": 80, "attributePosition": "auto", "bracketSpacing": true}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": false, "complexity": {"noExtraBooleanCast": "error", "noMultipleSpacesInRegularExpressionLiterals": "error", "noUselessCatch": "error", "noWith": "error"}, "correctness": {"noConstAssign": "error", "noConstantCondition": "error", "noEmptyCharacterClassInRegex": "error", "noEmptyPattern": "error", "noGlobalObjectCalls": "error", "noInvalidBuiltinInstantiation": "error", "noInvalidConstructorSuper": "error", "noNonoctalDecimalEscape": "error", "noPrecisionLoss": "error", "noSelfAssign": "error", "noSetterReturn": "error", "noSwitchDeclarations": "error", "noUndeclaredVariables": "error", "noUnreachable": "error", "noUnreachableSuper": "error", "noUnsafeFinally": "error", "noUnsafeOptionalChaining": "error", "noUnusedLabels": "error", "noUnusedPrivateClassMembers": "error", "noUnusedVariables": "error", "useIsNan": "error", "useValidForDirection": "error", "useYield": "error"}, "suspicious": {"noAsyncPromiseExecutor": "error", "noCatchAssign": "error", "noClassAssign": "error", "noCompareNegZero": "error", "noControlCharactersInRegex": "error", "noDebugger": "error", "noDuplicateCase": "error", "noDuplicateClassMembers": "error", "noDuplicateObjectKeys": "error", "noDuplicateParameters": "error", "noEmptyBlockStatements": "error", "noFallthroughSwitchClause": "error", "noFunctionAssign": "error", "noGlobalAssign": "error", "noImportAssign": "error", "noMisleadingCharacterClass": "error", "noPrototypeBuiltins": "error", "noRedeclare": "error", "noShadowRestrictedNames": "error", "noSparseArray": "error", "noUnsafeNegation": "error", "useGetterReturn": "error", "useValidTypeof": "error"}}, "ignore": ["**/src/common/schema.ts"]}, "javascript": {"formatter": {"jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "trailingCommas": "es5", "semicolons": "asNeeded", "arrowParentheses": "always", "bracketSameLine": false, "quoteStyle": "double", "attributePosition": "auto", "bracketSpacing": true}}, "overrides": [{"include": ["apps/web/src/**/*.ts", "apps/web/src/**/*.tsx"], "ignore": ["apps/web/src/common/schema.ts", "apps/web/src/routeTree.gen.ts"], "formatter": {"indentStyle": "space", "indentWidth": 4}, "linter": {"rules": {"nursery": {"useSortedClasses": {"level": "error", "fix": "safe", "options": {"attributes": ["className"], "functions": ["cn"]}}}, "a11y": {"noAriaUnsupportedElements": "error", "useAltText": "error", "useAriaPropsForRole": "error", "useValidAriaProps": "error", "useValidAriaValues": "error", "noBlankTarget": "off"}, "correctness": {"useExhaustiveDependencies": "warn", "useHookAtTopLevel": "error", "noInvalidUseBeforeDeclaration": "off"}, "style": {"noImplicitBoolean": "off", "useBlockStatements": "off", "useConsistentArrayType": {"level": "error", "options": {"syntax": "generic"}}, "useEnumInitializers": "error", "useExportType": "error", "useImportType": "error"}, "complexity": {"noForEach": "off", "noUselessEmptyExport": "error", "noUselessThisAlias": "error", "noUselessTypeConstraint": "error"}, "suspicious": {"noSelfCompare": "error", "noExplicitAny": "error", "noExtraNonNullAssertion": "error", "noMisleadingInstantiator": "error", "noUnsafeDeclarationMerging": "error", "useAwait": "error", "useNamespaceKeyword": "error"}}}, "javascript": {"globals": ["location", "window", "document"]}}, {"include": ["apps/server/**/*.ts", "apps/server/**/*.tsx", "apps/server/**/*.mts", "apps/server/**/*.cts"], "formatter": {"indentStyle": "space", "indentWidth": 4}, "linter": {"rules": {"style": {"noInferrableTypes": "off", "noNamespace": "error", "useAsConstAssertion": "error", "useConst": "error", "noArguments": "error", "noVar": "error"}, "suspicious": {"noExplicitAny": "error", "noExtraNonNullAssertion": "error", "noMisleadingInstantiator": "error", "noUnsafeDeclarationMerging": "error", "useNamespaceKeyword": "error"}, "complexity": {"noUselessTypeConstraint": "error"}, "correctness": {"useArrayLiterals": "off"}}}}, {"include": ["**/*.ts", "**/*.tsx", "**/*.mts", "**/*.cts"], "linter": {"rules": {"correctness": {"noConstAssign": "off", "noGlobalObjectCalls": "off", "noInvalidBuiltinInstantiation": "off", "noInvalidConstructorSuper": "off", "noNewSymbol": "off", "noSetterReturn": "off", "noUndeclaredVariables": "off", "noUnreachable": "off", "noUnreachableSuper": "off"}, "suspicious": {"noClassAssign": "off", "noDuplicateClassMembers": "off", "noDuplicateObjectKeys": "off", "noDuplicateParameters": "off", "noFunctionAssign": "off", "noImportAssign": "off", "noRedeclare": "off", "noUnsafeNegation": "off", "useGetterReturn": "off"}}}}]}