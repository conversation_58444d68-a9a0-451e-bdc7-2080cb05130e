# Ticket: Setup Database Models for Financial Entities

## Description
Create the core database models required for the financial tracking functionality of Centry. This includes models for accounts, transactions, categories, tags, and currencies. These models will form the foundation of the application's data structure.

## Background
Centry needs a robust data model to support its financial tracking capabilities. The current codebase has authentication and organization models, but lacks the specific financial entities needed for the core functionality described in the PRD.

## Requirements
- Create database models for the following entities:
  - Financial Account (checking, savings, credit card, etc.)
  - Transaction (income, expense, transfer)
  - Category (hierarchical structure)
  - Tag
  - Currency

- Each model should include appropriate relationships and constraints:
  - Accounts belong to users and/or households
  - Transactions are linked to accounts, categories, and tags
  - Categories can have parent-child relationships
  - Tags are many-to-many with transactions

## Acceptance Criteria
- [ ] Database schema files created for all required models
- [ ] Models include all necessary fields as specified in the PRD
- [ ] Proper relationships established between models
- [ ] Migration files generated and tested
- [ ] TypeScript types and Zod schemas generated for all models
- [ ] Documentation added for the data model structure

## Technical Notes
- Use the existing pattern in the codebase for defining models with Drizzle ORM
- Follow the established naming conventions
- Ensure proper indexing for performance
- Consider future extensibility in the model design

## Dependencies
- Existing authentication and organization models

## Estimation
- Story Points: 5
- Time Estimate: 2-3 days

## Priority
High - This is a foundational task that other features will depend on.
