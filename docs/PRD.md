## **Product Requirements Document: Centry**

Version: 1.0

Date: May 7, 2025

Author: GeminiStatus: Draft

**1. Introduction & Overview**

Centry is a web-based financial management application designed for individuals, families, and small groups (e.g., housemates, travel groups) who seek a comprehensive, collaborative, and intelligent platform to track, manage, and understand their finances. It goes beyond traditional personal finance tools by offering robust multi-user "Household" functionalities, flexible transaction organization with categories and tags, multi-currency support for global usability, and AI-driven insights to simplify financial management and promote better financial habits.

The core problem Centry addresses is the need for a unified, adaptable, and smart system that can handle the complexities of modern financial life, including shared expenses, international transactions, and the desire for deeper understanding without manual drudgery.

**2. Goals & Objectives**

- **Primary Goal:** To empower users with a clear, collaborative, and intelligent platform for managing their complete financial picture, leading to improved financial literacy, better decision-making, and achievement of financial goals.

- **Facilitate Collaboration:** Enable seamless financial management and transparency within user-defined "Households" or groups.

- **Provide Comprehensive Tracking:** Accurately track income, expenses, and transfers across various accounts and currencies.

- **Offer Flexible Organization:** Allow users to categorize and tag transactions in a way that best suits their individual or group needs.

- **Simplify Data Entry & Integration:** Offer easy data import from external sources with custom mapping and leverage AI for smart categorization.

- **Deliver Actionable Insights:** Utilize AI to provide personalized and group-level financial insights, identify trends, and suggest areas for improvement.

- **Ensure Global Usability:** Support multiple currencies for users who travel or manage finances across borders.

- **Maintain Security & Privacy:** Implement robust security measures to protect sensitive financial data, with clear permission controls within Households.

**3. Target Audience**

- **Individuals:** Seeking a sophisticated tool to manage personal finances, track spending across multiple currencies, and gain deeper insights.

- **Families/Couples:** Needing a shared platform to manage household budgets, track joint expenses and income, and work towards common financial goals (e.g., saving for a house, vacation).

- **Housemates/Shared Living Arrangements:** Requiring a transparent way to track shared bills (rent, utilities, groceries) and individual contributions.

- **Small Groups/Teams:** Organizing finances for group trips, projects, or small informal clubs.

- **Tech-Savvy Users:** Appreciate AI-driven features, customizability, and robust data import/export capabilities.

- **Users with International Lifestyles:** Frequently deal with multiple currencies due to travel, work, or family abroad.

**4. User Stories / Use Cases**

- **As an Individual User, I want to:**

* Quickly log my income, expenses, and transfers between my accounts (e.g., checking to savings).

* Categorize my transactions (e.g., "Groceries," "Transport") and add multiple tags (e.g., #work, #projectX, #manila\_trip) for granular tracking.

* Set a default currency (e.g., PHP) but be able to log transactions in other currencies (e.g., USD, JPY) when I travel, with automatic or manual conversion rates.

* Import my bank statements (CSV) and map the columns to Centry fields so I don't have to enter everything manually.

* Have the AI suggest categories for my imported and manually entered transactions.

* See a dashboard with my spending trends, net worth (assets vs. liabilities), and AI-generated insights about my habits.

- **As a Household Admin (e.g., Parent, Primary Budget Holder), I want to:**

* Create a "Household" (e.g., "Smith Family," "Apartment 4B Crew").

* Invite other members (e.g., spouse, children, housemates) to the Household.

* Define which accounts are "shared" within the Household and which remain "personal" (visible only to me).

* Set permissions for Household members (e.g., view-only for shared accounts, can add transactions to shared accounts).

* Track shared expenses and see who contributed what.

* View consolidated reports for the Household's finances.

* Receive AI insights on the Household's collective spending patterns and progress towards shared goals.

- **As a Household Member, I want to:**

* Join a Household I've been invited to.

* View and add transactions to shared accounts as per my permissions.

* Keep my personal accounts separate and private if desired, even within a Household context.

* Easily see my share of expenses or contributions to shared goals.

- **As any User, I want to:**

* Define custom categories and tags.

* See exchange rates applied to foreign currency transactions and their equivalent in my primary currency.

* Generate reports (e.g., spending by category/tag, income vs. expense) for my personal finances or for Households I'm part of.

* Securely access my financial data from any web browser.

**5. Features & Functionality**

5.1. Core Financial Tracking:

\* Transaction Entry:

\* Types: Income, Expense, Transfer (between user's own accounts/assets).

\* Fields: Date, Description, Amount, Currency, Category, Tags (multi-select), Account (source/destination), Notes.

\* For Transfers: Source Account, Destination Account, Amount (can be same or different currency with conversion).

\* Accounts Management:

\* Create various account types (e.g., Cash, Bank Account, Credit Card, E-Wallet, Investment, Loan).

\* Set initial balance and currency for each account.

\* Link accounts to Households (as shared or personal).

5.2. Organization System:

\* Categories:

\* User-definable hierarchical categories (e.g., Food > Groceries, Food > Dining Out).

\* Assign a default category type (Income/Expense).

\* Tags:

\* User-definable, free-form tags for cross-cutting concerns (e.g., #vacation2025, #clientABC, #urgent).

\* Transactions can have multiple tags.

5.3. Multi-Currency Support:

\* Set a primary/default currency for the user account.

\* Log transactions in any supported currency.

\* Automatic fetching of daily exchange rates (from a reliable API) for conversion to the primary currency for reporting.

\* Option for manual exchange rate input for specific transactions.

\* Reports can display values in original currency and/or primary currency.

5.4. Households & Collaboration:

\* Household Creation & Management:

\* Create Households with a name and description.

\* Invite users to a Household via email or username.

\* Assign roles/permissions within a Household (e.g., Admin, Member, Viewer).

\* Admins: Manage members, settings, accounts.

\* Members: Add/edit transactions to shared accounts.

\* Viewers: View shared account data only.

\* Shared & Personal Accounts:

\* Users can designate their accounts as "Personal" (visible only to them) or "Shared" with specific Households.

\* Transactions in shared accounts are visible to Household members based on their permissions.

\* Split Expenses (Future Iteration - V1.1): Feature to easily split a single expense among multiple Household members.

5.5. AI Integration:

\* AI-Powered Smart Categorization:

\* Suggests categories (and potentially tags) for new transactions based on description, merchant, amount, and historical user/Household patterns.

\* Learns from user corrections.

\* AI-Driven Financial Insights:

\* Personalized insights on spending habits, saving opportunities, unusual transactions.

\* Household-level insights on collective spending, budget adherence (if budgets are implemented), and goal progress.

\* Anomaly detection (e.g., "Your 'Dining Out' spending is 30% higher this month than your 6-month average for the 'Smith Family' Household.").

\* AI for Import Assistance (Future Iteration - V1.1):

\* Intelligently suggest mappings for imported CSV columns.

\* Potentially identify recurring transactions during import for subscription tracking.

5.6. Data Import & Export:

\* CSV Import:

\* Upload CSV files from banks or other financial tools.

\* Custom Mapping: User interface to map CSV columns (e.g., "Transaction Detail," "Debit Amount") to Centry fields (Description, Amount, Date, etc.).

\* Save mapping templates for specific bank formats for future reuse.

\* Data Export: Export transaction data to CSV/Excel for personal backup or analysis.

5.7. Reporting & Dashboards:

\* Personal Dashboard: Overview of individual financial health, recent transactions, spending by category, progress towards personal goals.

\* Household Dashboard: Consolidated view for shared finances, contributions, spending patterns within the Household.

\* Customizable Reports:

\* Income vs. Expense.

\* Spending by Category/Tag.

\* Cash Flow Statement.

\* Net Worth (simple calculation based on account balances).

\* Filter reports by date range, account, category, tag, currency, Household.

5.8. User Accounts & Security:

\* Secure user registration and login (email/password, OAuth option e.g., Google).

\* Password recovery.

\* Data encryption at rest and in transit.

\* Role-based access control within Households.

\* Two-Factor Authentication (2FA) option.

**6. AI Integration Details (Elaboration)**

- **Smart Categorization:**

* **Technology:** NLP for transaction descriptions. Machine learning model (e.g., text classifier like Naive Bayes, SVM, or a simple neural network) trained on user/Household data.

* **Training:** Model trains continuously on user-confirmed categorizations. A global pre-trained model can provide initial suggestions for common vendors/descriptions, which then gets fine-tuned per user/Household.

- **Financial Insights Engine:**

* **Technology:** Combination of rule-based systems (for common insights like "top spending categories"), statistical analysis (for trend identification, averages, deviations), and potentially anomaly detection algorithms.

* **Personalization:** Insights are tailored based on the user's or Household's specific data and defined goals.

- **Data for AI:** All AI processing will be done on the server-side. User data used for training AI models will be handled according to strict privacy policies, ideally anonymized or aggregated where possible for global model improvements, and always siloed for personal/Household model fine-tuning.

**7. Design & UX Considerations (Web App)**

- **Responsive Design:** Fully functional and aesthetically pleasing on desktop, tablet, and mobile browsers.

- **Intuitive Navigation:** Clear and consistent site structure.

- **Data Visualization:** Use clear and interactive charts/graphs for dashboards and reports (e.g., using libraries like Chart.js, D3.js, or Recharts).

- **Efficiency:** Streamlined forms for transaction entry and data import.

- **Clarity in Collaboration:** Easy to understand which data is personal vs. shared, and what permissions are active.

- **Customization:** Allow users to personalize their dashboard view and reporting preferences.

- **Accessibility (WCAG):** Strive for AA compliance.

**8. Technical Considerations (High-Level)**

- **Tech Stack (Example):**

* Frontend: React, Vue, or Angular.

* Backend: Python (Django/Flask), Node.js (Express), or Ruby on Rails.

* Database: PostgreSQL or MySQL (for relational data), potentially a NoSQL DB for certain types of flexible data if needed.

* AI/ML: Python libraries (scikit-learn, spaCy, NLTK, TensorFlow/PyTorch).

- **Deployment:** Cloud-based (AWS, Google Cloud, Azure) for scalability and reliability.

- **API Design:** Well-documented RESTful or GraphQL API for frontend-backend communication and potential future integrations.

- **Background Jobs:** For tasks like fetching exchange rates, processing large imports, or generating complex AI insights.

**9. Monetization / Value Proposition**

- **For a personal project:** The value is in creating a tailored tool that perfectly fits your needs and serves as a learning experience.

- **If commercialized (hypothetical):**

* Freemium model: Core features free for individuals, limited Households/members.

* Subscription Tiers:

- Premium Individual: Advanced AI insights, unlimited categories/tags, more import sources.

- Household/Group Plan: More members per Household, advanced collaborative features, budget controls.

**10. Success Metrics (for the User/Developer)**

- **Adoption:** Consistent use by yourself and any invited Household members.

- **Feature Usage:** Regular use of core features like multi-currency, categories/tags, Households, and AI-driven categorization.

- **Data Accuracy:** Confidence in the financial data and reports generated.

- **Time Saved:** Reduction in manual effort for financial tracking due to import and AI features.

- **Improved Financial Literacy/Behavior:** Evidence of better financial decisions or goal achievement based on insights from the app.

- **User Satisfaction:** Positive feedback from any users (including yourself!) on ease of use, feature set, and AI effectiveness.

**11. Future Considerations / Potential Roadmap (Post-MVP)**

- **Budgeting Module:** Create detailed budgets for categories, track spending against budgets for individuals and Households.

- **Advanced Goal Setting:** More sophisticated goal tracking, including projections and "what-if" scenarios.

- **Subscription Tracker:** AI-powered identification and management of recurring subscriptions.

- **Debt Paydown Planner:** Tools to strategize and track debt repayment.

- **Investment Tracking:** More detailed tracking of investment portfolios and performance.

- **Mobile Native Apps:** Dedicated iOS/Android apps for enhanced mobile experience (could leverage the web API).

- **Direct Bank Integration (Open Banking/Plaid-like services):** Automatic transaction syncing (highly complex, regional availability varies).

- **Enhanced AI Forecasting:** Predictive insights on cash flow, potential savings, etc.

- **Gamification:** Badges or rewards for achieving financial milestones.

- **Receipt OCR via Mobile/Webcam:** Directly upload receipt images for AI-powered data extraction.

This PRD for Centry lays a comprehensive foundation for a powerful, collaborative, and intelligent financial web application. Remember to prioritize features for an MVP and iterate based on your own needs and feedback if you involve others!
