# K22i App (Minimal Version)

A modern, full-stack web application template built with TypeScript, featuring a robust backend API and a responsive frontend interface. This monorepo structure enables efficient development across multiple packages and applications.

## 🌟 Overview

K22i App provides a complete foundation for building modern web applications with best practices:

- **Type Safety**: End-to-end TypeScript for robust code quality
- **Modular Architecture**: Clean separation of concerns with a monorepo structure
- **Developer Experience**: Fast development workflow with hot reloading
- **Performance**: Optimized build process and runtime performance
- **Scalability**: Designed to scale from small projects to large applications

## 🚀 Tech Stack

### Backend (Server)

- **Runtime**: [Bun](https://bun.sh/) - Fast all-in-one JavaScript runtime
- **Framework**: [Hono](https://hono.dev/) - Fast, lightweight, and type-safe web framework
- **Database**: PostgreSQL with [Drizzle ORM](https://orm.drizzle.team/) for type-safe database operations
- **Authentication**: BetterAuth for secure user authentication
- **API Documentation**: OpenAPI/Swagger with <PERSON>ala<PERSON> for interactive API docs
- **Validation**: <PERSON><PERSON> for runtime type validation

### Frontend (Web)

- **Framework**: React with Vite for fast development
- **UI Components**: shadcn/ui for beautiful, accessible components
- **Data Management**: TanStack Query for efficient data fetching
- **Routing**: TanStack Router for type-safe routing
- **Forms**: React Hook Form with Zod validation
- **Styling**: Tailwind CSS for utility-first styling
- **Testing**: Vitest (Unit) & Playwright (E2E)

## 📦 Prerequisites

- [Bun](https://bun.sh/) (JavaScript runtime & package manager)
- [Docker](https://www.docker.com/) (for containerization)

## 🛠️ Setup & Installation

1. Clone the repository
   ```bash
   git clone https://github.com/yourusername/k22i-app-minimal.git
   cd k22i-app-minimal
   ```

2. Run the bootstrap script to install dependencies and set up the project
   ```bash
   ./bootstrap.sh
   ```

3. Start the development environment
   ```bash
   ./run.sh dev
   ```

4. Access the applications:
   - Frontend: http://localhost:3333
   - Server: http://localhost:8888
   - API Documentation: http://localhost:8888/docs

## 🏗️ Project Structure

```
.
├── apps/               # Application services
│   ├── server/         # Backend service
│   │   ├── src/        # Source code
│   │   └── scripts/    # Utility scripts
│   └── web/            # Frontend application
│       ├── src/        # Source code
│       ├── public/     # Static assets
│       └── e2e/        # End-to-end tests
├── packages/           # Shared packages
│   ├── config/         # Configuration utilities
│   ├── database/       # Database schema and migrations
│   ├── lib/            # Shared utilities and helpers
│   └── ui/             # Shared UI components
├── docker-compose.yml  # Development container config
└── run.sh              # Development management script
```

## 📝 Development Commands

### General
- `./run.sh dev` - Start development environment
- `./run.sh stop` - Stop all running containers

### Database Management
- `./run.sh db:reset` - Complete database reset
- `./run.sh db:migrate` - Run database migrations
- `./run.sh db:generate` - Generate new migrations
- `./run.sh db:seed` - Seed database with initial data

### Web Development
- `./run.sh web:install` - Install web dependencies
- `./run.sh web:component` - Add new shadcn component
- `./run.sh web:generate` - Generate OpenAPI schema

### Server Development
- `./run.sh server:install` - Install server dependencies
- `./run.sh server:module` - Create new server module
- `./run.sh server:pms:new` - Create post-migration script
- `./run.sh server:pms:run` - Run post-migration scripts

## 🚀 Deployment

The project includes Docker configurations for both development and production:

- `docker-compose.yml` - Development environment setup
- `docker-compose.deploy.yml` - Production deployment setup

Refer to individual service documentation for specific deployment instructions:
- [Server Service Documentation](apps/server/README.md)
- [Web Application Documentation](apps/web/README.md)
