import{baseModel as e}from"@k22i/database/columns";import{relations as t}from"drizzle-orm";import{bigint as a,index as o,pgTable as r,text as n,timestamp as s,uuid as i}from"drizzle-orm/pg-core";import{organization as l,user as d}from"../__models/__auth.models";import{category as c}from"../__models/__categories.models";import{currency as m}from"../__models/__currencies.models";import{transactionTagAssociation as u}from"../__models/__tags.models";import{wallet as f}from"../__models/__wallets.models";export const transaction=r("transaction",{...e,transactionDate:s().notNull(),description:n().notNull(),amount:a({mode:"bigint"}).notNull(),currencyId:i().notNull().references(()=>m.id),categoryId:i().notNull().references(()=>c.id),userId:i().notNull().references(()=>d.id),organizationId:i().notNull().references(()=>l.id),sourceWalletId:i().references(()=>f.id),destinationWalletId:i().references(()=>f.id),estimatedDefaultCurrencyValue:a({mode:"bigint"}),currencyExchangeRate:a({mode:"bigint"})},e=>[o().on(e.userId),o().on(e.organizationId),o().on(e.sourceWalletId),o().on(e.destinationWalletId)]);export const transactionRelations=t(transaction,({one:e,many:t})=>({currency:e(m,{fields:[transaction.currencyId],references:[m.id]}),category:e(c,{fields:[transaction.categoryId],references:[c.id]}),transactionTagAssociation:t(u),user:e(d,{fields:[transaction.userId],references:[d.id]}),organization:e(l,{fields:[transaction.organizationId],references:[l.id]}),sourceWallet:e(f,{fields:[transaction.sourceWalletId],references:[f.id],relationName:"sourceWallet"}),destinationWallet:e(f,{fields:[transaction.destinationWalletId],references:[f.id],relationName:"destinationWallet"})}));