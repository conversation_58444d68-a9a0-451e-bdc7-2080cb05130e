import{baseModel as o}from"@k22i/database/columns";import{relations as a}from"drizzle-orm";import{pgTable as r,text as t,uuid as e}from"drizzle-orm/pg-core";import{organization as n}from"../__models/__auth.models";import{transaction as s}from"../__models/__transactions.models";import{colorEnum as l}from"../__models/__wallets.models";export const category=r("category",{...o,name:t().notNull(),type:t().notNull(),organizationId:e().notNull().references(()=>n.id),color:l().notNull(),icon:t().notNull()});export const categoryRelations=a(category,({many:o,one:a})=>({transactions:o(s),organization:a(n,{fields:[category.organizationId],references:[n.id]})}));