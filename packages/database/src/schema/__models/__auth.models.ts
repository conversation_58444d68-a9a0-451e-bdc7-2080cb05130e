import{baseModel as e}from"@k22i/database/columns";import{relations as n}from"drizzle-orm";import{bigint as i,boolean as o,index as r,integer as t,pgTable as s,text as a,timestamp as u,uniqueIndex as l,uuid as d}from"drizzle-orm/pg-core";export const user=s("user",{...e,name:a("name").notNull(),email:a("email").notNull().unique(),emailVerified:o("email_verified").notNull(),image:a("image"),role:a("role"),banned:o("banned"),banReason:a("ban_reason"),banExpires:u("ban_expires"),customerId:a("customer_id").unique()},e=>[l().on(e.email)]);export const userRelations=n(user,({many:e})=>({accounts:e(account),sessions:e(session),members:e(member),invitations:e(invitation)}));export const session=s("session",{...e,expiresAt:u("expires_at").notNull(),token:a("token").notNull(),ipAddress:a("ip_address"),userAgent:a("user_agent"),userId:d("user_id").notNull().references(()=>user.id),impersonatedBy:a("impersonated_by"),activeOrganizationId:d("active_organization_id").references(()=>organization.id)},e=>[r().on(e.userId),l().on(e.token)]);export const sessionRelations=n(session,({one:e})=>({user:e(user,{fields:[session.userId],references:[user.id]})}));export const account=s("account",{...e,accountId:a("account_id").notNull(),providerId:a("provider_id").notNull(),userId:d("user_id").notNull().references(()=>user.id),accessToken:a("access_token"),refreshToken:a("refresh_token"),idToken:a("id_token"),accessTokenExpiresAt:u("access_token_expires_at"),refreshTokenExpiresAt:u("refresh_token_expires_at"),scope:a("scope"),password:a("password")},e=>[r().on(e.userId)]);export const accountRelations=n(account,({one:e})=>({user:e(user,{fields:[account.userId],references:[user.id]})}));export const verification=s("verification",{...e,identifier:a("identifier").notNull(),value:a("value").notNull(),expiresAt:u("expires_at").notNull()},e=>[r().on(e.identifier)]);export const organization=s("organization",{...e,name:a("name").notNull(),slug:a("slug"),logo:a("logo"),metadata:a("metadata")},e=>[r().on(e.slug)]);export const organizationRelations=n(organization,({many:e})=>({members:e(member),invitations:e(invitation)}));export const member=s("member",{...e,organizationId:d("organization_id").notNull().references(()=>organization.id),userId:d("user_id").notNull().references(()=>user.id),role:a("role").notNull()},e=>[l().on(e.organizationId,e.userId),r().on(e.organizationId),r().on(e.userId)]);export const memberRelations=n(member,({one:e})=>({user:e(user,{fields:[member.userId],references:[user.id]}),organization:e(organization,{fields:[member.organizationId],references:[organization.id]})}));export const invitation=s("invitation",{...e,organizationId:d("organization_id").notNull().references(()=>organization.id),email:a("email").notNull(),role:a("role"),status:a("status").notNull(),expiresAt:u("expires_at").notNull(),inviterId:d("inviter_id").notNull().references(()=>user.id)},e=>[l().on(e.organizationId,e.email),r().on(e.organizationId),r().on(e.inviterId)]);export const invitationRelations=n(invitation,({one:e})=>({organization:e(organization,{fields:[invitation.organizationId],references:[organization.id]}),inviter:e(user,{fields:[invitation.inviterId],references:[user.id]})}));export const rateLimit=s("rate_limit",{...e,key:a("key"),count:t("count"),lastRequest:i("last_request",{mode:"number"})});