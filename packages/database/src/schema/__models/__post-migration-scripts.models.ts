import{sql as t}from"drizzle-orm";import{pgEnum as o,pgTable as r,text as e,timestamp as i}from"drizzle-orm/pg-core";export const postMigrationScriptStatusEnum=o("post_migration_script_state",["pending","success","failed"]);export const postMigrationScript=r("post_migration_script",{id:e().primaryKey().default(t`gen_random_uuid()`),fileName:e().notNull(),createdAt:i().notNull().defaultNow(),completedAt:i(),errorMessage:e(),status:postMigrationScriptStatusEnum().notNull().default("pending")});