import{baseModel as a}from"@k22i/database/columns";import{relations as o}from"drizzle-orm";import{index as t,pgTable as n,text as s,uniqueIndex as i,uuid as r}from"drizzle-orm/pg-core";import{organization as e}from"../__models/__auth.models";import{transaction as c}from"../__models/__transactions.models";import{colorEnum as l}from"../__models/__wallets.models";export const tag=n("tag",{...a,name:s().notNull(),organizationId:r().notNull().references(()=>e.id),color:l().notNull()});export const transactionTagAssociation=n("transaction_tag_association",{transactionId:r().notNull().references(()=>c.id),tagId:r().notNull().references(()=>tag.id)},a=>[i().on(a.transactionId,a.tagId),t().on(a.transactionId),t().on(a.tagId)]);export const tagRelations=o(tag,({many:a})=>({transactionTagAssociation:a(transactionTagAssociation)}));export const transactionTagAssociationRelations=o(transactionTagAssociation,({one:a})=>({transaction:a(c,{fields:[transactionTagAssociation.transactionId],references:[c.id]}),tag:a(tag,{fields:[transactionTagAssociation.tagId],references:[tag.id]})}));