import{baseModel as e}from"@k22i/database/columns";import{relations as l}from"drizzle-orm";import{bigint as o,pgEnum as t,pgTable as a,text as r,uuid as n}from"drizzle-orm/pg-core";import{organization as s}from"../__models/__auth.models";import{transaction as i}from"../__models/__transactions.models";export const colorEnum=t("color",["slate","gray","zinc","neutral","stone","red","orange","amber","yellow","lime","green","emerald","teal","cyan","sky","blue","indigo","violet","purple","fuchsia","pink","rose"]);export const walletGroupTypeEnum=t("wallet_group_type",["asset","debt"]);export const walletGroup=a("wallet_group",{...e,name:r().notNull(),type:walletGroupTypeEnum().notNull(),organizationId:n().notNull().references(()=>s.id)});export const walletGroupRelations=l(walletGroup,({many:e})=>({wallets:e(wallet)}));export const wallet=a("wallet",{...e,name:r().notNull(),walletGroupId:n().notNull().references(()=>walletGroup.id),balance:o({mode:"bigint"}).notNull()});export const walletRelations=l(wallet,({one:e,many:l})=>({walletGroup:e(walletGroup,{fields:[wallet.walletGroupId],references:[walletGroup.id]}),sourceTransactions:l(i,{relationName:"sourceWallet"}),destinationTransactions:l(i,{relationName:"destinationWallet"})}));