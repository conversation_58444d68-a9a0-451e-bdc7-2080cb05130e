import{baseModel as o}from"@k22i/database/columns";import{relations as r}from"drizzle-orm";import{integer as t,pgTable as l,text as a}from"drizzle-orm/pg-core";import{transaction as e}from"../__models/__transactions.models";export const currency=l("currency",{...o,symbol:a().notNull(),name:a().notNull(),symbolNative:a().notNull(),decimalDigits:t().notNull(),code:a().notNull(),namePlural:a().notNull()});export const currencyRelations=r(currency,({many:o})=>({transactions:o(e)}));