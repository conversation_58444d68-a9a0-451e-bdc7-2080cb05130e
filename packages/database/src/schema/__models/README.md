# Schema Models

This directory contains the schema model definitions used for database operations. These files are direct copies of the models from `/app/api`, synchronized using the scripts in `/scripts/duplicate-models`.

## Purpose

The models are duplicated here to simplify the schema generation process. This approach ensures:

1. Consistent model definitions across the application
2. Simplified schema generation without complex dependencies
3. Easy maintenance through automated synchronization

## Important Notes

- Do not modify the files in this directory directly
- All changes should be made to the original models in `/app/api`
- Run the duplication script to update these files when the original models change

## Synchronization

This directory is auto-populated while `/.run.sh dev` is running.
It is populate through `watcher.js` inside `/apps/server`.
