import * as path from "path"
import { logger } from "@k22i/lib"
import chalk from "chalk"
import * as fs from "fs/promises"

const capitalize = (str: string): string =>
    str.charAt(0).toUpperCase() + str.slice(1)

interface ModelInfo {
    name: string
    isEnum: boolean
    sourceFile: string
}

async function scanModelsDirectory(
    directory: string
): Promise<Array<ModelInfo>> {
    const models: Array<ModelInfo> = []
    const files = await fs.readdir(directory)

    for (const file of files) {
        if (file.endsWith(".ts")) {
            const filePath = path.join(directory, file)
            const content = await fs.readFile(filePath, "utf-8")

            // Find all exports
            const exportMatches =
                content.match(/export const\s+(\w+)\s*=/g) || []

            for (const match of exportMatches) {
                const name = match
                    .replace(/export const\s+/, "")
                    .replace(/\s*=/, "")
                // For enums, we need to look for a specific pattern:
                // export const name = pgEnum("enum_name", ["value1", "value2", ...])
                // or in minified files:
                // export const name=o("enum_name",["value1","value2",...]

                // First, check if the name ends with "Enum" or "StatusEnum" as a heuristic
                const mightBeEnum =
                    name.endsWith("Enum") || name.includes("StatusEnum")

                let isEnum = false

                if (mightBeEnum) {
                    // Look for the specific pattern of enum definition
                    // This works for both regular and minified files
                    const enumDefPattern = new RegExp(
                        `export const\\s+${name}\\s*=\\s*(\\w+)\\(["']\\w+["'],\\s*\\[`
                    )
                    const enumDefMatch = content.match(enumDefPattern)

                    if (enumDefMatch) {
                        isEnum = true

                        // Enum detected
                    }
                }
                models.push({
                    name,
                    isEnum,
                    sourceFile: path.basename(file, ".ts"),
                })
            }
        }
    }

    return models
}

const generateSchemaStrings = (models: Array<ModelInfo>): string => {
    // Group models by source file
    const modelsByFile = models.reduce(
        (acc, model) => {
            if (
                !model.name.endsWith("Relations") &&
                !model.name.toLowerCase().includes("relation")
            ) {
                if (!acc[model.sourceFile]) {
                    acc[model.sourceFile] = []
                }
                if (acc[model.sourceFile]) {
                    acc[model.sourceFile]!.push(model)
                }
            }
            return acc
        },
        {} as Record<string, Array<ModelInfo>>
    )

    // Generate consolidated imports
    const modelImports = Object.entries(modelsByFile)
        .map(([file, fileModels]) => {
            const modelNames = fileModels.map((m) => m.name).join(", ")
            // Remove the "__" prefix from the file name
            const fileName = file.replace(/^__/, "")
            return `import { ${modelNames} } from "../../../database/src/schema/__models/__${fileName}"
`
        })
        .join("")

    let output = `/*
    * This file is auto-generated by scripts/generate-schemas.ts
    * Do not edit this file manually
*/
import { createInsertSchema, createSelectSchema } from "drizzle-zod"
import { z } from "zod"
${modelImports}`

    for (const model of models) {
        const capitalizedName = capitalize(model.name)

        if (model.isEnum) {
            // For enums, only create the schema and type, not the insert schema
            output += `export const ${capitalizedName}Schema = z.enum(${model.name}.enumValues)
`
            output += `export type ${capitalizedName} = z.infer<typeof ${capitalizedName}Schema>
`
        } else if (
            model.name.endsWith("Relations") ||
            model.name.toLowerCase().includes("relation")
        ) {
            continue // Skip relations
        } else {
            output += `export const ${capitalizedName}Schema = createSelectSchema(${model.name})
`
            output += `export const ${capitalizedName}InsertSchema = createInsertSchema(${model.name})
`
            output += `export type ${capitalizedName} = z.infer<typeof ${capitalizedName}Schema>
`
            output += `export type ${capitalizedName}Insert = z.infer<typeof ${capitalizedName}InsertSchema>
`
        }
    }

    return output
}

async function main() {
    const [modelsDir, outputFile] = process.argv.slice(2)

    if (!modelsDir || !outputFile) {
        logger.error({
            msg: "Usage: bun run ./packages/database/scripts/generate-schemas.ts <models_directory> <output_file>",
        })
        throw new Error("Invalid arguments")
    }

    try {
        const models = await scanModelsDirectory(modelsDir)
        const schemas = generateSchemaStrings(models)
        await fs.writeFile(outputFile, schemas)
        logger.info({
            msg: "Schemas generated successfully",
            outputPath: outputFile,
        })
    } catch (error) {
        if (error instanceof Error) {
            console.error(chalk.red("Error:"), chalk.redBright(error.message))
        } else {
            console.error(error)
        }
    }
}

main()
