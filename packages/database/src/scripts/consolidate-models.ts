import * as path from "path"
import { logger } from "@k22i/lib"
import chalk from "chalk"
import * as fs from "fs/promises"

async function findModelsFiles(dir: string): Promise<Array<string>> {
    const files = await fs.readdir(dir, { withFileTypes: true })
    const modelsFiles: Array<string> = []

    for (const file of files) {
        const fullPath = path.join(dir, file.name)
        if (file.isDirectory()) {
            modelsFiles.push(...(await findModelsFiles(fullPath)))
        } else if (file.name.endsWith(".models.ts")) {
            modelsFiles.push(fullPath)
        }
    }

    return modelsFiles
}

async function extractExports(filePath: string): Promise<Set<string>> {
    const content = await fs.readFile(filePath, "utf-8")
    const exportLines = content
        .split("\n")
        .filter((line) => line.trim().startsWith("export"))

    const exports = new Set<string>()

    for (const line of exportLines) {
        const trimmedLine = line.trim()
        if (trimmedLine.startsWith("export const ")) {
            const match = trimmedLine.match(/export const (\w+)/)
            if (!match) {
                continue
            }
            if (!match[1]) {
                throw new Error("match[1] is empty")
            }
            exports.add(match[1])
        } else if (trimmedLine.startsWith("export {")) {
            const match = trimmedLine.match(/export\s*{([^}]+)}/)
            if (!match) {
                continue
            }
            if (!match[1]) {
                throw new Error("match[1] is empty")
            }
            const items = match[1].split(",").map((item) => item.trim())
            items.forEach((item) => exports.add(item))
        }
    }

    return exports
}

async function consolidateExports(
    inputDir: string,
    outputFile: string
): Promise<void> {
    logger.info({
        msg: "Searching for models.ts files...",
        inputDir,
    })
    const modelsFiles = await findModelsFiles(inputDir)

    logger.info({
        msg: "Found model files",
        modelsFiles,
    })

    const allExports: Map<string, Set<string>> = new Map()

    for (const file of modelsFiles) {
        const exports = await extractExports(file)
        const relativePath = path.relative(process.cwd(), file)
        const modulePath = `@/${relativePath
            .replace(/\\/g, "/")
            .replace(/\.ts$/, "")
            // TODO: line below is a workaround. ideally we should save path correctly
            .replace("src/", "")}`

        if (exports.size > 0) {
            allExports.set(modulePath, exports)
        }
    }

    const consolidatedExports = Array.from(allExports.entries()).map(
        ([modulePath, exports]) => {
            return `export { ${Array.from(exports).join(", ")} } from "./__models/__${modulePath.split("/")[modulePath.split("/").length - 1]}"`
        }
    )
    const autogeneratedComment = `/*
 * This file is automatically generated by scripts/consolidate-models.ts
 * Do not edit this file manually
 */
`
    const outputContent =
        autogeneratedComment + consolidatedExports.join("\n") + "\n"
    await fs.writeFile(outputFile, outputContent, "utf-8")

    logger.info({
        msg: "Consolidated exports written to",
        outputFile,
    })
}

async function main() {
    const [inputFile, outputFile] = process.argv.slice(2)

    if (!inputFile || !outputFile) {
        console.error(
            chalk.red(
                "Usage: bun run ./scripts/consolidate-models.ts <input_directory> <output_file>"
            )
        )
        throw new Error("Invalid arguments")
    }

    try {
        await consolidateExports(inputFile, outputFile)
    } catch (error) {
        console.error(chalk.red("An error occurred:"), error)
        throw error
    }
}

main()
