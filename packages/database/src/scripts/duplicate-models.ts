import { readdir } from "node:fs/promises"
import { join } from "node:path"
import { logger } from "@k22i/lib"

async function findModelsFiles(dir: string): Promise<Array<string>> {
    const files = await readdir(dir, { withFileTypes: true })
    const modelsFiles: Array<string> = []

    for (const file of files) {
        const fullPath = join(dir, file.name)
        if (file.isDirectory()) {
            modelsFiles.push(...(await findModelsFiles(fullPath)))
        } else if (file.name.endsWith(".models.ts")) {
            modelsFiles.push(fullPath)
        }
    }

    return modelsFiles
}

/**
 * Transforms imports from @/modules/module_name/module_name.models to ../modules/__module_name.models
 * Also transforms imports from relative paths like ../../auth/auth.models
 */
function transformImports(content: string): string {
    // Regular expression to match imports from @/modules/*/module_name.models
    const importRegex1 =
        /import\s+{([^}]+)}\s+from\s+["']@\/modules\/([^\/]+)\/[^"']+\.models["']/g

    // Replace imports with @/modules format
    let transformedContent = content.replace(
        importRegex1,
        (_match, importedItems, moduleName) => {
            return `import {${importedItems}} from "../__models/__${moduleName}.models"`
        }
    )

    // Handle specific case for subscriptions.models.ts
    transformedContent = transformedContent.replace(
        /import\s+{([^}]+)}\s+from\s+["']\.\.\/\.\.\/auth\/auth\.models["']/g,
        (_match, importedItems) => {
            return `import {${importedItems}} from "../__models/__auth.models"`
        }
    )

    // Handle all relative imports to auth.models
    transformedContent = transformedContent.replace(
        /import\s+{([^}]+)}\s+from\s+["'](?:\.\.\/)+auth\/auth\.models["']/g,
        (_match, importedItems) => {
            return `import {${importedItems}} from "../__models/__auth.models"`
        }
    )

    // Handle all other relative imports to *.models
    const relativeImportRegex =
        /import\s+{([^}]+)}\s+from\s+["'](?:\.\.\/)+([^\/]+)\/[^"']+\.models["']/g
    transformedContent = transformedContent.replace(
        relativeImportRegex,
        (_match, importedItems, moduleName) => {
            return `import {${importedItems}} from "../__models/__${moduleName}.models"`
        }
    )

    // Handle the specific case we encountered with __models/__models.models
    transformedContent = transformedContent.replace(
        /import\s+{([^}]+)}\s+from\s+["']\.\.\/\_\_models\/\_\_\_\_models\.models["']/g,
        (_match, importedItems) => {
            return `import {${importedItems}} from "../__models/__auth.models"`
        }
    )

    return transformedContent
}

async function duplicateModels(sourceDir: string, targetDir: string) {
    try {
        logger.info({
            msg: "Starting model files duplication...",
            sourceDir,
            targetDir,
        })

        const modelFiles = await findModelsFiles(sourceDir)

        logger.info({
            msg: "Found model files",
            count: modelFiles.length,
            files: modelFiles,
        })

        for (const file of modelFiles) {
            const fileName = file.split("/").pop()
            if (!fileName) continue

            const sourceFile = Bun.file(file)
            let content = await sourceFile.text()

            // Transform imports
            content = transformImports(content)

            await Bun.write(join(targetDir, `__${fileName}`), content)

            logger.info({
                msg: "Duplicated model file",
                source: file,
                target: join(targetDir, `__${fileName}`),
            })
        }

        logger.info({
            msg: "Model files duplication completed",
        })
    } catch (error) {
        logger.error({
            msg: "Error during model files duplication",
            error,
        })
        throw error
    }
}

async function main() {
    const sourceDir = join(process.cwd(), "apps", "server", "src", "modules")
    const targetDir = join(
        process.cwd(),
        "packages",
        "database",
        "src",
        "schema",
        "__models"
    )

    try {
        await duplicateModels(sourceDir, targetDir)
    } catch (error) {
        console.error("Failed to duplicate models:", error)
        process.exit(1)
    }
}

main()
