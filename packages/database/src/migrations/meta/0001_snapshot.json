{"id": "30cd30e4-c432-4d14-852e-f192cc8f337a", "prevId": "123ff05c-4a4b-40b0-8866-ca75a23ab91d", "version": "7", "dialect": "postgresql", "tables": {"public.account": {"name": "account", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token_expires_at": {"name": "access_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "refresh_token_expires_at": {"name": "refresh_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"account_user_id_index": {"name": "account_user_id_index", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"account_user_id_user_id_fk": {"name": "account_user_id_user_id_fk", "tableFrom": "account", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.category": {"name": "category", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "color": {"name": "color", "type": "color", "typeSchema": "public", "primaryKey": false, "notNull": true}, "icon": {"name": "icon", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"category_organization_id_organization_id_fk": {"name": "category_organization_id_organization_id_fk", "tableFrom": "category", "tableTo": "organization", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.currency": {"name": "currency", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "symbol": {"name": "symbol", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "symbol_native": {"name": "symbol_native", "type": "text", "primaryKey": false, "notNull": true}, "decimal_digits": {"name": "decimal_digits", "type": "integer", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "name_plural": {"name": "name_plural", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.invitation": {"name": "invitation", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "inviter_id": {"name": "inviter_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {"invitation_organization_id_email_index": {"name": "invitation_organization_id_email_index", "columns": [{"expression": "organization_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "invitation_organization_id_index": {"name": "invitation_organization_id_index", "columns": [{"expression": "organization_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "invitation_inviter_id_index": {"name": "invitation_inviter_id_index", "columns": [{"expression": "inviter_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"invitation_organization_id_organization_id_fk": {"name": "invitation_organization_id_organization_id_fk", "tableFrom": "invitation", "tableTo": "organization", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "invitation_inviter_id_user_id_fk": {"name": "invitation_inviter_id_user_id_fk", "tableFrom": "invitation", "tableTo": "user", "columnsFrom": ["inviter_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.member": {"name": "member", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"member_organization_id_user_id_index": {"name": "member_organization_id_user_id_index", "columns": [{"expression": "organization_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "member_organization_id_index": {"name": "member_organization_id_index", "columns": [{"expression": "organization_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "member_user_id_index": {"name": "member_user_id_index", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"member_organization_id_organization_id_fk": {"name": "member_organization_id_organization_id_fk", "tableFrom": "member", "tableTo": "organization", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "member_user_id_user_id_fk": {"name": "member_user_id_user_id_fk", "tableFrom": "member", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.organization": {"name": "organization", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": false}, "logo": {"name": "logo", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"organization_slug_index": {"name": "organization_slug_index", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.post_migration_script": {"name": "post_migration_script", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "file_name": {"name": "file_name", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "post_migration_script_state", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.rate_limit": {"name": "rate_limit", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "key": {"name": "key", "type": "text", "primaryKey": false, "notNull": false}, "count": {"name": "count", "type": "integer", "primaryKey": false, "notNull": false}, "last_request": {"name": "last_request", "type": "bigint", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.session": {"name": "session", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "impersonated_by": {"name": "impersonated_by", "type": "text", "primaryKey": false, "notNull": false}, "active_organization_id": {"name": "active_organization_id", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {"session_user_id_index": {"name": "session_user_id_index", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "session_token_index": {"name": "session_token_index", "columns": [{"expression": "token", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"session_user_id_user_id_fk": {"name": "session_user_id_user_id_fk", "tableFrom": "session", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "session_active_organization_id_organization_id_fk": {"name": "session_active_organization_id_organization_id_fk", "tableFrom": "session", "tableTo": "organization", "columnsFrom": ["active_organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tag": {"name": "tag", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "color": {"name": "color", "type": "color", "typeSchema": "public", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"tag_organization_id_organization_id_fk": {"name": "tag_organization_id_organization_id_fk", "tableFrom": "tag", "tableTo": "organization", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.transaction": {"name": "transaction", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "transaction_date": {"name": "transaction_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "bigint", "primaryKey": false, "notNull": true}, "currency_id": {"name": "currency_id", "type": "uuid", "primaryKey": false, "notNull": true}, "category_id": {"name": "category_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "source_wallet_id": {"name": "source_wallet_id", "type": "uuid", "primaryKey": false, "notNull": false}, "destination_wallet_id": {"name": "destination_wallet_id", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {"transaction_user_id_index": {"name": "transaction_user_id_index", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "transaction_organization_id_index": {"name": "transaction_organization_id_index", "columns": [{"expression": "organization_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "transaction_source_wallet_id_index": {"name": "transaction_source_wallet_id_index", "columns": [{"expression": "source_wallet_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "transaction_destination_wallet_id_index": {"name": "transaction_destination_wallet_id_index", "columns": [{"expression": "destination_wallet_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"transaction_currency_id_currency_id_fk": {"name": "transaction_currency_id_currency_id_fk", "tableFrom": "transaction", "tableTo": "currency", "columnsFrom": ["currency_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "transaction_category_id_category_id_fk": {"name": "transaction_category_id_category_id_fk", "tableFrom": "transaction", "tableTo": "category", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "transaction_user_id_user_id_fk": {"name": "transaction_user_id_user_id_fk", "tableFrom": "transaction", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "transaction_organization_id_organization_id_fk": {"name": "transaction_organization_id_organization_id_fk", "tableFrom": "transaction", "tableTo": "organization", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "transaction_source_wallet_id_wallet_id_fk": {"name": "transaction_source_wallet_id_wallet_id_fk", "tableFrom": "transaction", "tableTo": "wallet", "columnsFrom": ["source_wallet_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "transaction_destination_wallet_id_wallet_id_fk": {"name": "transaction_destination_wallet_id_wallet_id_fk", "tableFrom": "transaction", "tableTo": "wallet", "columnsFrom": ["destination_wallet_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.transaction_tag_association": {"name": "transaction_tag_association", "schema": "", "columns": {"transaction_id": {"name": "transaction_id", "type": "uuid", "primaryKey": false, "notNull": true}, "tag_id": {"name": "tag_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {"transaction_tag_association_transaction_id_tag_id_index": {"name": "transaction_tag_association_transaction_id_tag_id_index", "columns": [{"expression": "transaction_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "tag_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "transaction_tag_association_transaction_id_index": {"name": "transaction_tag_association_transaction_id_index", "columns": [{"expression": "transaction_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "transaction_tag_association_tag_id_index": {"name": "transaction_tag_association_tag_id_index", "columns": [{"expression": "tag_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"transaction_tag_association_transaction_id_transaction_id_fk": {"name": "transaction_tag_association_transaction_id_transaction_id_fk", "tableFrom": "transaction_tag_association", "tableTo": "transaction", "columnsFrom": ["transaction_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "transaction_tag_association_tag_id_tag_id_fk": {"name": "transaction_tag_association_tag_id_tag_id_fk", "tableFrom": "transaction_tag_association", "tableTo": "tag", "columnsFrom": ["tag_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user": {"name": "user", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": false}, "banned": {"name": "banned", "type": "boolean", "primaryKey": false, "notNull": false}, "ban_reason": {"name": "ban_reason", "type": "text", "primaryKey": false, "notNull": false}, "ban_expires": {"name": "ban_expires", "type": "timestamp", "primaryKey": false, "notNull": false}, "customer_id": {"name": "customer_id", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"user_email_index": {"name": "user_email_index", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_email_unique": {"name": "user_email_unique", "nullsNotDistinct": false, "columns": ["email"]}, "user_customer_id_unique": {"name": "user_customer_id_unique", "nullsNotDistinct": false, "columns": ["customer_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verification": {"name": "verification", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {"verification_identifier_index": {"name": "verification_identifier_index", "columns": [{"expression": "identifier", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.wallet": {"name": "wallet", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "wallet_group_id": {"name": "wallet_group_id", "type": "uuid", "primaryKey": false, "notNull": true}, "balance": {"name": "balance", "type": "bigint", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"wallet_wallet_group_id_wallet_group_id_fk": {"name": "wallet_wallet_group_id_wallet_group_id_fk", "tableFrom": "wallet", "tableTo": "wallet_group", "columnsFrom": ["wallet_group_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.wallet_group": {"name": "wallet_group", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "wallet_group_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"wallet_group_organization_id_organization_id_fk": {"name": "wallet_group_organization_id_organization_id_fk", "tableFrom": "wallet_group", "tableTo": "organization", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.color": {"name": "color", "schema": "public", "values": ["slate", "gray", "zinc", "neutral", "stone", "red", "orange", "amber", "yellow", "lime", "green", "emerald", "teal", "cyan", "sky", "blue", "indigo", "violet", "purple", "fuchsia", "pink", "rose"]}, "public.post_migration_script_state": {"name": "post_migration_script_state", "schema": "public", "values": ["pending", "success", "failed"]}, "public.wallet_group_type": {"name": "wallet_group_type", "schema": "public", "values": ["asset", "debt"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}