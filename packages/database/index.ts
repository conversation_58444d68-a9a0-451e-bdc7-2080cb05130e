import { drizzle } from "drizzle-orm/postgres-js"
import type { PostgresJsDatabase } from "drizzle-orm/postgres-js"
import postgres from "postgres"
import * as schema from "./src/schema"

declare global {
    var globalDb: PostgresJsDatabase<typeof schema> | undefined
}

if (!process.env["DATABASE_URL"]) {
    throw new Error("DATABASE_URL is not set")
}

let db: PostgresJsDatabase<typeof schema>

export function initializeDb() {
    const dbUrl = process.env["DATABASE_URL"]
    if (!dbUrl) {
        throw new Error("DATABASE_URL is not set")
    }

    // Always create a new connection in test environment
    if (process.env.NODE_ENV === "test") {
        db = drizzle(postgres(dbUrl, { prepare: true }), {
            schema,
            logger: false,
            casing: "snake_case",
        })
        global.globalDb = db
        return db
    }

    // For production, create a new connection
    if (process.env.NODE_ENV === "production") {
        db = drizzle(postgres(dbUrl, { prepare: true }), {
            schema,
        })
        return db
    }

    // For development, reuse the connection if it exists
    if (!global.globalDb) {
        global.globalDb = drizzle(postgres(dbUrl, { prepare: true }), {
            schema,
            logger: true,
            casing: "snake_case",
        })
    }

    db = global.globalDb
    return db
}

// Initialize the database connection
if (process.env.NODE_ENV === "production") {
    db = drizzle(postgres(process.env["DATABASE_URL"], { prepare: true }), {
        schema,
    })
} else {
    if (!global.globalDb) {
        global.globalDb = drizzle(
            postgres(process.env["DATABASE_URL"], { prepare: true }),
            {
                schema,
                logger: process.env.NODE_ENV === "test" ? false : true,
                casing: "snake_case",
            }
        )
    }

    db = global.globalDb
}

export { db, schema }
