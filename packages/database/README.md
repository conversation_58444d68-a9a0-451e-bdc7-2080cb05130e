# Database Package

Shared database schema and utilities for the K22i application, built with Drizzle ORM and PostgreSQL.

## 🚀 Features

- Centralized database schema definitions
- Type-safe database operations with Drizzle ORM
- Migration management
- Shared database utilities and helpers
- Schema validation with Zod integration

## 📦 Installation

```bash
bun install
```

## 🔧 Usage

Import database schema and utilities in your application:

```ts
import { db, schema } from "@k22i/database";

// Query the database
const users = await db.select().from(schema.users);

// Use schema for type-safe operations
const newUser = await db.insert(schema.users).values({
  name: "<PERSON>",
  email: "<EMAIL>",
});
```

## 📊 Schema Management

The package includes:

- **Schema Definitions**: Type-safe table schemas
- **Migrations**: Database migration scripts
- **Seeds**: Initial data seeding utilities
- **Relations**: Table relationship definitions
- **Queries**: Reusable query builders

## 🛠️ Development

To modify the database schema:

1. Update schema definitions in the appropriate files
2. Generate migrations using `bun run db:generate`
3. Apply migrations with `bun run db:migrate`

This package is part of the K22i application monorepo.
