# Configuration Package

Shared configuration utilities for the K22i application, providing consistent settings across the monorepo.

## 🚀 Features

- TypeScript type definitions for configuration options
- Environment variable validation and typing
- Shared ESLint, Prettier, and TypeScript configurations

## 📦 Installation

```bash
bun install
```

## 📋 Available Configurations

The package includes various configuration presets:

- **TypeScript**: Shared compiler options
- **Tailwind**: Base Tailwind CSS configuration
- **Environment**: Environment variable schemas and validation

## 🛠️ Development

To modify configurations, update the appropriate files in this package. Changes will be applied across all packages and applications that depend on this configuration.

This package is part of the K22i application monorepo.
