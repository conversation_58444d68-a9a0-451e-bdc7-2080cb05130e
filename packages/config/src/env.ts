import "dotenv/config"
import { z } from "zod"

import path from "path"
import { fileURLToPath } from "url"
import { config } from "dotenv"

// Get the absolute path to the root directory
const __dirname = path.dirname(fileURLToPath(import.meta.url))
const rootDir = path.resolve(__dirname, "../../../")

// Load .env from root directory
config({ path: path.join(rootDir, ".env") })

const envSchema = z.object({
    LOG_LEVEL: z
        .enum(["trace", "debug", "info", "warn", "error"])
        .default("info"),
    PORT: z.coerce.number().default(8888),
    ANALYZE: z
        .enum(["true", "false"])
        .optional()
        .transform((value) => value === "true"),
    NODE_ENV: z
        .enum(["development", "staging", "test", "production"])
        .default("development"),
    DATABASE_URL: z.string(),

    BASE_URL: z.string().default("localhost:8888"),
    ORIGIN_URL: z.string().url().default("http://localhost:3333"),

    // RabbitMQ Configuration
    RABBITMQ_HOST: z.string().default("rabbitmq"),
    RABBITMQ_PORT: z.coerce.number().default(5672),
    RABBITMQ_USER: z.string().default("guest"),
    RABBITMQ_PASSWORD: z.string().default("guest"),
    RABBITMQ_VHOST: z.string().default("/"),

    // Sentry/Glitchtip Configuration
    MONITORING_DSN: z.string().url().optional(),

    ENABLE_BACKGROUND_JOBS: z
        .enum(["true", "false"])
        .default("false")
        .transform((value) => value === "true"),
})

const env = envSchema.parse(process.env)
export { env }
