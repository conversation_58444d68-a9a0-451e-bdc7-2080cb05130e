import { LoaderCircle as LoaderIcon } from "lucide-react"
import type { FC } from "react"
import { cn } from "../lib/utils"

type LoaderProps = {
    size?: number
    full?: boolean
}

const Loader: FC<LoaderProps> = ({ size = 20, full = false }) => {
    return (
        <div
            className={cn(
                "w-fit h-fit",
                full && "w-full h-dvh flex items-center justify-center"
            )}
        >
            <div className="size-fit animate-spin">
                <LoaderIcon size={size} />
            </div>
        </div>
    )
}

export default Loader
