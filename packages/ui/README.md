# UI Components Package

Shared UI components for the K22i application, built with React, Tailwind CSS, and shadcn/ui.

## 🚀 Features

- Reusable UI components based on shadcn/ui
- Fully typed with TypeScript
- Styled with Tailwind CSS
- Accessible and responsive design

## 📦 Installation

```bash
bun install
```

## 🔧 Usage

Import components in your application:

```tsx
import { Button } from "@k22i/ui";

function MyComponent() {
  return <Button>Click me</Button>;
}
```

## 🧩 Available Components

The package includes various UI components that follow consistent design patterns:

- Buttons and form controls
- Layout components
- Navigation elements
- Data display components
- Feedback indicators

## 🛠️ Development

To add new components or modify existing ones, follow the project's component guidelines.

This package is part of the K22i application monorepo.
