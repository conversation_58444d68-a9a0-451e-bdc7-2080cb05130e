# Shared Library Package

Common utilities and helper functions for the K22i application, providing reusable code across the monorepo.

## 🚀 Features

- Shared utility functions
- Common type definitions
- Reusable hooks and helpers
- API client utilities
- Date, string, and number formatting helpers

## 📦 Installation

```bash
bun install
```

## 🔧 Usage

Import utilities in your application:

```ts
import { formatDate, createSlug, validateEmail } from "@k22i/lib";

// Format a date
const formattedDate = formatDate(new Date(), "yyyy-MM-dd");

// Create a URL-friendly slug
const slug = createSlug("Hello World");

// Validate an email address
const isValid = validateEmail("<EMAIL>");
```

## 📚 Available Utilities

The package includes various utility categories:

- **String Utilities**: Text manipulation, validation, formatting
- **Date Utilities**: Date formatting, parsing, manipulation
- **Number Utilities**: Numeric formatting, calculations, conversions
- **Array Utilities**: Collection operations and transformations
- **Object Utilities**: Object manipulation and transformation
- **Validation Utilities**: Input validation helpers
- **API Utilities**: API request helpers and error handling

## 🛠️ Development

To add new utilities or modify existing ones, follow the project's code organization guidelines. Ensure all utilities are properly typed and tested.

This package is part of the K22i application monorepo.
