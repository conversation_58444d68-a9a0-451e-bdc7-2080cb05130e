/*
 * This file is auto-generated by scripts/generate-schemas.ts
 * Do not edit this file manually
 */
import { createInsertSchema, createSelectSchema } from "drizzle-zod"
import { z } from "zod"
import {
    account,
    invitation,
    member,
    organization,
    rateLimit,
    session,
    user,
    verification,
} from "../../../database/src/schema/__models/__auth.models"
import { category } from "../../../database/src/schema/__models/__categories.models"
import { currency } from "../../../database/src/schema/__models/__currencies.models"
import { householdSetting } from "../../../database/src/schema/__models/__households.models"
import {
    postMigrationScript,
    postMigrationScriptStatusEnum,
} from "../../../database/src/schema/__models/__post-migration-scripts.models"
import {
    tag,
    transactionTagAssociation,
} from "../../../database/src/schema/__models/__tags.models"
import { transaction } from "../../../database/src/schema/__models/__transactions.models"
import {
    colorEnum,
    wallet,
    walletGroup,
    walletGroupTypeEnum,
} from "../../../database/src/schema/__models/__wallets.models"
export const ColorEnumSchema = z.enum(colorEnum.enumValues)
export type ColorEnum = z.infer<typeof ColorEnumSchema>
export const WalletGroupTypeEnumSchema = z.enum(walletGroupTypeEnum.enumValues)
export type WalletGroupTypeEnum = z.infer<typeof WalletGroupTypeEnumSchema>
export const WalletGroupSchema = createSelectSchema(walletGroup)
export const WalletGroupInsertSchema = createInsertSchema(walletGroup)
export type WalletGroup = z.infer<typeof WalletGroupSchema>
export type WalletGroupInsert = z.infer<typeof WalletGroupInsertSchema>
export const WalletSchema = createSelectSchema(wallet)
export const WalletInsertSchema = createInsertSchema(wallet)
export type Wallet = z.infer<typeof WalletSchema>
export type WalletInsert = z.infer<typeof WalletInsertSchema>
export const CategorySchema = createSelectSchema(category)
export const CategoryInsertSchema = createInsertSchema(category)
export type Category = z.infer<typeof CategorySchema>
export type CategoryInsert = z.infer<typeof CategoryInsertSchema>
export const HouseholdSettingSchema = createSelectSchema(householdSetting)
export const HouseholdSettingInsertSchema = createInsertSchema(householdSetting)
export type HouseholdSetting = z.infer<typeof HouseholdSettingSchema>
export type HouseholdSettingInsert = z.infer<
    typeof HouseholdSettingInsertSchema
>
export const PostMigrationScriptStatusEnumSchema = z.enum(
    postMigrationScriptStatusEnum.enumValues
)
export type PostMigrationScriptStatusEnum = z.infer<
    typeof PostMigrationScriptStatusEnumSchema
>
export const PostMigrationScriptSchema = createSelectSchema(postMigrationScript)
export const PostMigrationScriptInsertSchema =
    createInsertSchema(postMigrationScript)
export type PostMigrationScript = z.infer<typeof PostMigrationScriptSchema>
export type PostMigrationScriptInsert = z.infer<
    typeof PostMigrationScriptInsertSchema
>
export const TransactionSchema = createSelectSchema(transaction)
export const TransactionInsertSchema = createInsertSchema(transaction)
export type Transaction = z.infer<typeof TransactionSchema>
export type TransactionInsert = z.infer<typeof TransactionInsertSchema>
export const UserSchema = createSelectSchema(user)
export const UserInsertSchema = createInsertSchema(user)
export type User = z.infer<typeof UserSchema>
export type UserInsert = z.infer<typeof UserInsertSchema>
export const SessionSchema = createSelectSchema(session)
export const SessionInsertSchema = createInsertSchema(session)
export type Session = z.infer<typeof SessionSchema>
export type SessionInsert = z.infer<typeof SessionInsertSchema>
export const AccountSchema = createSelectSchema(account)
export const AccountInsertSchema = createInsertSchema(account)
export type Account = z.infer<typeof AccountSchema>
export type AccountInsert = z.infer<typeof AccountInsertSchema>
export const VerificationSchema = createSelectSchema(verification)
export const VerificationInsertSchema = createInsertSchema(verification)
export type Verification = z.infer<typeof VerificationSchema>
export type VerificationInsert = z.infer<typeof VerificationInsertSchema>
export const OrganizationSchema = createSelectSchema(organization)
export const OrganizationInsertSchema = createInsertSchema(organization)
export type Organization = z.infer<typeof OrganizationSchema>
export type OrganizationInsert = z.infer<typeof OrganizationInsertSchema>
export const MemberSchema = createSelectSchema(member)
export const MemberInsertSchema = createInsertSchema(member)
export type Member = z.infer<typeof MemberSchema>
export type MemberInsert = z.infer<typeof MemberInsertSchema>
export const InvitationSchema = createSelectSchema(invitation)
export const InvitationInsertSchema = createInsertSchema(invitation)
export type Invitation = z.infer<typeof InvitationSchema>
export type InvitationInsert = z.infer<typeof InvitationInsertSchema>
export const RateLimitSchema = createSelectSchema(rateLimit)
export const RateLimitInsertSchema = createInsertSchema(rateLimit)
export type RateLimit = z.infer<typeof RateLimitSchema>
export type RateLimitInsert = z.infer<typeof RateLimitInsertSchema>
export const CurrencySchema = createSelectSchema(currency)
export const CurrencyInsertSchema = createInsertSchema(currency)
export type Currency = z.infer<typeof CurrencySchema>
export type CurrencyInsert = z.infer<typeof CurrencyInsertSchema>
export const TagSchema = createSelectSchema(tag)
export const TagInsertSchema = createInsertSchema(tag)
export type Tag = z.infer<typeof TagSchema>
export type TagInsert = z.infer<typeof TagInsertSchema>
export const TransactionTagAssociationSchema = createSelectSchema(
    transactionTagAssociation
)
export const TransactionTagAssociationInsertSchema = createInsertSchema(
    transactionTagAssociation
)
export type TransactionTagAssociation = z.infer<
    typeof TransactionTagAssociationSchema
>
export type TransactionTagAssociationInsert = z.infer<
    typeof TransactionTagAssociationInsertSchema
>
