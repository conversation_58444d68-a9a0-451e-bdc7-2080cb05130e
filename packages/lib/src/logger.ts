import { env } from "@k22i/config/env"
import pino from "pino"

const commonLoggerOptions = {
    level: env.NODE_ENV === "development" ? "debug" : "info",
}

const baseLogger = pino({
    ...commonLoggerOptions,
})

const prettyLogger = pino({
    ...commonLoggerOptions,
    formatters: {
        level: (label) => {
            return { level: label }
        },
    },
    transport:
        env.NODE_ENV === "development" || env.NODE_ENV === "test"
            ? {
                  target: "pino-pretty",
                  options: {
                      colorize: true,
                  },
              }
            : undefined,
})

const logger =
    env.NODE_ENV === "development" || env.NODE_ENV === "test"
        ? prettyLogger
        : baseLogger

export { logger }
