pre-commit:
    parallel: true
    commands:
        typecheck-web:
            root: "apps/web/"
            glob: "*.{js,ts,jsx,tsx}"
            run: bun run typecheck
        typecheck-server:
            root: "apps/server/"
            glob: "*.{js,ts,jsx,tsx}"
            run: bun run typecheck
        biome:
            root: ""
            glob: "*.{js,ts,jsx,tsx,json}"
            run: |
                bunx biome check --write --staged {staged_files}
                if [ -n "$(git diff {staged_files})" ]; then
                  echo "⛔️ Biome made changes. Please stage the changes and try again."
                  exit 1
                fi
        typecheck-package-ui:
            root: "packages/ui/"
            glob: "*.{js,ts,jsx,tsx}"
            run: bun tsc --noEmit
        typecheck-package-database:
            root: "packages/database/"
            glob: "*.{js,ts,jsx,tsx}"
            run: bun tsc --noEmit
        typecheck-package-lib:
            root: "packages/lib/"
            glob: "*.{js,ts,jsx,tsx}"
            run: bun tsc --noEmit
        typecheck-package-config:
            root: "packages/config/"
            glob: "*.{js,ts,jsx,tsx}"
            run: bun tsc --noEmit
        actionlint:
            glob: ".github/workflows/*.{yml,yaml}"
            run: actionlint {staged_files}
