name: Staging Pipeline

on:
  push:
    branches: [canary]
  pull_request:
    branches: [canary]

jobs:
  deploy:
    uses: ./.github/workflows/deploy.yml
    with:
      environment: staging
      branch: staging
    secrets:
      # SHARED AMONG DIFFERENT ENVIRONMENTS
      DOKPLOY_TOKEN: ${{ secrets.DOKPLOY_TOKEN }}
      DATABASE_EXTERNAL_PORT: ${{ secrets.DATABASE_EXTERNAL_PORT }}
      # ENV SPECIFIC
      DATABASE_URL: ${{ secrets.STAGING_DATABASE_URL }}
      DOKPLOY_POSTGRES_ID: ${{ secrets.DOKPLOY_STAGING_POSTGRES_ID }}
      DOKPLOY_FRONTEND_SERVICE_ID: ${{ secrets.DOKPLOY_STAGING_FRONTEND_SERVICE_ID }}
      DOKPLOY_BACKEND_SERVICE_ID: ${{ secrets.DOKPLOY_STAGING_BACKEND_SERVICE_ID }}
