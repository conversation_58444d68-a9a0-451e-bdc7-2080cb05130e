name: Production Pipeline

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  deploy:
    uses: ./.github/workflows/deploy.yml
    with:
      environment: production
      branch: main
    secrets:
      # SHARED AMONG DIFFERENT ENVIRONMENTS
      DOKPLOY_TOKEN: ${{ secrets.DOKPLOY_TOKEN }}
      DATABASE_EXTERNAL_PORT: ${{ secrets.DATABASE_EXTERNAL_PORT }}
      # ENV SPECIFIC
      DATABASE_URL: ${{ secrets.PRODUCTION_DATABASE_URL }}
      DOKPLOY_POSTGRES_ID: ${{ secrets.DOKPLOY_PRODUCTION_POSTGRES_ID }}
      DOKPLOY_FRONTEND_SERVICE_ID: ${{ secrets.DOKPLOY_PRODUCTION_FRONTEND_SERVICE_ID }}
      DOKPLOY_BACKEND_SERVICE_ID: ${{ secrets.DOKPLOY_PRODUCTION_BACKEND_SERVICE_ID }}
