name: PR Backend Tests

on:
  pull_request:
    types: [opened, synchronize, reopened]
    paths:
      - "apps/server/**"
      - "packages/**"
      - ".github/workflows/pr-tests.yml"

jobs:
  test-backend:
    name: Run Backend Tests
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest

      - name: Install dependencies
        run: bun install

      - name: Run backend tests
        run: bun run --cwd apps/server test
        env:
          # TestContainers will create and manage its own PostgreSQL instance
          # These environment variables are still needed for the test setup
          NODE_ENV: test
          BASE_URL: http://localhost:8888
          ORIGIN_URL: http://localhost:3333
