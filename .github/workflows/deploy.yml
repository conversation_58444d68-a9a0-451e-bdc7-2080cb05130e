name: Deployment Pipeline

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
      branch:
        required: true
        type: string
    secrets:
      DATABASE_URL:
        required: true
      DOKPLOY_TOKEN:
        required: true
      DOKPLOY_POSTGRES_ID:
        required: true
      DOKPLOY_FRONTEND_SERVICE_ID:
        required: true
      DOKPLOY_BACKEND_SERVICE_ID:
        required: true
      DATABASE_EXTERNAL_PORT:
        required: true
      DISCORD_WEBHOOK_URL:
        required: false

env:
  NODE_VERSION: "20.x"
  DATABASE_URL: ${{ secrets.DATABASE_URL }}
  ENV_PREFIX: ${{ inputs.environment }}

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest
      - name: Install Dependencies
        run: bun install
      - name: Type Check
        run: |
          bun run --cwd apps/server typecheck
          bun run --cwd apps/web typecheck
      - name: Lint
        run: |
          bun run --cwd apps/server lint
          bun run --cwd apps/web lint

  build:
    needs: test
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == format('refs/heads/{0}', inputs.branch)
    steps:
      - uses: actions/checkout@v4
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest
      - name: Install Dependencies
        run: bun install
      - name: Build Server
        run: bun run build:server
      - name: Build Web
        run: bun run build:web
      - name: Upload Server Build Artifact
        uses: actions/upload-artifact@v4
        with:
          name: server-build
          path: apps/server/dist
      - name: Upload Web Build Artifact
        uses: actions/upload-artifact@v4
        with:
          name: web-build
          path: apps/web/dist

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == format('refs/heads/{0}', inputs.branch)
    environment: ${{ inputs.environment }}
    steps:
      - uses: actions/checkout@v4
      - name: Download Server Build
        uses: actions/download-artifact@v4
        with:
          name: server-build
          path: apps/server/dist
      - name: Download Web Build
        uses: actions/download-artifact@v4
        with:
          name: web-build
          path: apps/web/dist
      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest
      - name: Install Dependencies
        run: bun install

  configure-database:
    needs: deploy
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == format('refs/heads/{0}', inputs.branch)
    outputs:
      port_configured: ${{ steps.add_port.outputs.status }}
    steps:
      - name: Add External Port to Database
        id: add_port
        run: |
          curl -f -X POST "https://deploy.k22i.net/api/postgres.saveExternalPort" \
            -H "x-api-key: ${{ secrets.DOKPLOY_TOKEN }}" \
            -H "Content-Type: application/json" \
            -d '{
              "postgresId": "${{ secrets.DOKPLOY_POSTGRES_ID }}",
              "externalPort": ${{ secrets.DATABASE_EXTERNAL_PORT }}
            }'
          echo "status=success" >> "$GITHUB_OUTPUT"

  run-migrations:
    needs: configure-database
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == format('refs/heads/{0}', inputs.branch)
    environment: ${{ inputs.environment }}
    steps:
      - uses: actions/checkout@v4
      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest
      - name: Install Dependencies
        run: bun install
      - name: Run Database Migrations
        run: bun run --cwd packages/database migrate
        env:
          DATABASE_URL: ${{ secrets.DATABASE_URL }}

  cleanup-and-deploy:
    needs: run-migrations
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == format('refs/heads/{0}', inputs.branch)
    environment: ${{ inputs.environment }}
    steps:
      - name: Clear Database External Port
        run: |
          curl -f -X POST "https://deploy.k22i.net/api/postgres.saveExternalPort" \
            -H "x-api-key: ${{ secrets.DOKPLOY_TOKEN }}" \
            -H "Content-Type: application/json" \
            -d '{
              "postgresId": "${{ secrets.DOKPLOY_POSTGRES_ID }}",
              "externalPort": 0
            }'
      - name: Trigger Deployment Webhook for the Backend
        run: |
          curl -f -X POST "https://deploy.k22i.net/api/application.deploy" \
            -H "x-api-key: ${{ secrets.DOKPLOY_TOKEN }}" \
            -H "Content-Type: application/json" \
            -d '{
              "applicationId": "${{ secrets.DOKPLOY_BACKEND_SERVICE_ID }}"
            }'
      - name: Trigger Deployment Webhook for the Frontend
        run: |
          curl -f -X POST "https://deploy.k22i.net/api/application.deploy" \
            -H "x-api-key: ${{ secrets.DOKPLOY_TOKEN }}" \
            -H "Content-Type: application/json" \
            -d '{
              "applicationId": "${{ secrets.DOKPLOY_FRONTEND_SERVICE_ID }}"
            }'

  discord-notification:
    needs:
      [
        test,
        build,
        deploy,
        configure-database,
        run-migrations,
        cleanup-and-deploy,
      ]
    if: failure()
    runs-on: ubuntu-latest
    steps:
      - name: Send Discord Notification
        uses: sarisia/actions-status-discord@v1
        env:
          DISCORD_WEBHOOK: ${{ secrets.DISCORD_WEBHOOK_URL }}
        if: ${{ env.DISCORD_WEBHOOK != '' }}
        with:
          webhook: ${{ secrets.DISCORD_WEBHOOK_URL }}
          status: ${{ job.status }}
          title: "❌ Medecia Pipeline Failed: ${{ inputs.environment }} environment"
          description: "Deployment pipeline for branch ${{ inputs.branch }} failed"
          color: 0xFF0000
          username: GitHub Actions
          avatar_url: https://github.githubassets.com/images/modules/logos_page/GitHub-Mark.png
