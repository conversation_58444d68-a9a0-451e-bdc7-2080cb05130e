{"name": "k22i-app-minimal", "devDependencies": {"@biomejs/biome": "1.9.4", "@swc/core": "^1.11.22", "concurrently": "^9.1.2"}, "private": true, "scripts": {"dev": "./run.sh dev", "dev:worker": "./run.sh dev:worker", "dev:all": "./run.sh dev:all", "build:web": "bun run --cwd apps/web build", "build:server": "bun run --cwd apps/server build", "start": "bun run --cwd apps/web serve & bun run --cwd apps/server start", "start:worker": "bun run --cwd apps/server worker.js", "start:all": "bun run start & bun run start:worker", "typecheck": "tsc --noEmit", "test": "bun test"}, "workspaces": ["apps/*", "packages/*"]}